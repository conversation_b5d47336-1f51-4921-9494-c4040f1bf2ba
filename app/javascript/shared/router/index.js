import { createRouter, createWebHashHistory } from "vue-router";
import { reactive, readonly } from "vue";
import ticketNavigationGuard from "./ticketNavigationGuard";
import { useEventStore } from "@/stores/event";

// Import dashboard components
import DashMain from "@/event-dashboard/components/DashMain.vue";
import DelegatesAttending from "@/event-dashboard/components/DelegatesAttending.vue";
import DelegatesUnconfirmed from "@/event-dashboard/components/DelegatesUnconfirmed.vue";
import DelegatesCancelled from "@/event-dashboard/components/DelegatesCancelled.vue";
import DelegatesDeclined from "@/event-dashboard/components/DelegatesDeclined.vue";
import DelegatesFailedPayment from "@/event-dashboard/components/DelegatesFailedPayment.vue";
import BookingsUnpaid from "@/event-dashboard/components/BookingsUnpaid.vue";
import AmendBookings from "@/event-dashboard/components/AmendBookings.vue";
import Payments from "@/event-dashboard/components/Payments.vue";
import DownloadPoller from "@/event-dashboard/components/DownloadPoller.vue";

// Import event creation components
import EventDetails from "@/events/components/details/EventDetails.vue";
import Tickets from "@/events/components/tickets/TicketsCreation.vue";
import RegQuestions from "@/events/components/additional/RegQuestions.vue";
import Terms from "@/events/components/terms/Terms.vue";
import EventPreview from "@/events/components/preview/EventPreview.vue";
import CustomiseEmails from "@/events/components/customise_emails/Start.vue";
import CustomiseInvite from "@/events/components/customise_emails/CustomiseInvite.vue";
import CustomiseConfirmation from "@/events/components/customise_emails/CustomiseConfirmation.vue";
import EmailPreview from "@/events/components/customise_emails/EmailPreview.vue";
import EventImageUpload from "@/events/components/EventImageUpload.vue";
import AttendeeTable from "@/event-dashboard/components/AttendeeTable.vue";

// Utils
// import { checkAuthentication, startSessionKeepAlive } from '@/events/utils/authHelper'

// Create reactive router state with Context7 pattern
const state = reactive({
  currentRoute: null,
  previousRoute: null,
  isNavigating: false,
});

// Actions for router state management
const actions = {
  setCurrentRoute(route) {
    state.previousRoute = state.currentRoute;
    state.currentRoute = route;
  },
  setNavigating(value) {
    state.isNavigating = value;
  },
};

// Define routes
const routes = [
  // Dashboard main route
  {
    path: "/:eventId?",
    component: DashMain,
    name: "dash-main",
    props: true,
  },

  // Event creation routes
  {
    path: "/event/new",
    name: "create-event",
    component: EventDetails,
  },
  {
    path: "/event/:eventId",
    name: "edit-event",
    component: EventDetails,
    props: true,
  },
  {
    path: "/tickets/:eventId?",
    name: "ticket-creation",
    component: Tickets,
    props: true,
  },
  {
    path: "/reg-questions/:eventId?",
    name: "reg-questions",
    component: RegQuestions,
    props: true,
  },
  {
    path: "/terms",
    component: Terms,
    props: { noredirect: true },
    name: "terms",
  },
  {
    path: "/event/:eventId/preview",
    name: "preview",
    component: EventPreview,
    props: true,
  },
  {
    path: "/event/:eventId/images",
    name: "event-images",
    component: EventImageUpload,
    props: true,
  },

  // Event management routes

  {
    path: "/attendees",
    component: AttendeeTable,
    name: "attendees",
  },
  {
    path: "/attendees-unconfirmed",
    component: DelegatesUnconfirmed,
    name: "attendees-unconfirmed",
  },
  {
    path: "/attendees-payment-failed",
    component: DelegatesFailedPayment,
    name: "attendees-payment-failed",
  },
  {
    path: "/bookings-unpaid",
    component: BookingsUnpaid,
    name: "bookings-unpaid",
  },
  {
    path: "/attendees-cancelled",
    component: DelegatesCancelled,
    name: "attendees-cancelled",
  },
  {
    path: "/attendees-declined",
    component: DelegatesDeclined,
    name: "attendees-declined",
  },
  {
    path: "/payments/:status",
    component: Payments,
    name: "payments",
  },

  {
    path: "/amend-bookings/:bookingId",
    name: "amend-bookings",
    component: AmendBookings,
    props: true,
  },

  // Email customization routes
  {
    path: "/customise-emails/:eventId?",
    component: CustomiseEmails,
    name: "customise-emails",
    props: true,
    redirect: { name: "upload-contacts" },
    children: [
      {
        path: "",
        name: "upload-contacts",
        component: CustomiseInvite,
        props: true,
      },
      {
        path: "customise-invite",
        name: "customise-invite",
        component: CustomiseInvite,
        props: true,
      },
      {
        path: "customise-confirmation",
        name: "customise-confirmation",
        component: CustomiseConfirmation,
        props: true,
      },
      {
        path: "email-preview",
        name: "email-preview",
        component: EmailPreview,
        props: true,
      },
    ],
  },
];

// Create router instance with hash mode for better Rails compatibility
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// Setup navigation guards to track route changes
router.beforeEach((to, from, next) => {
  actions.setNavigating(true);
  console.log(`Router navigation: ${from.name} -> ${to.name}`, {
    from: {
      name: from.name,
      params: from.params,
      path: from.path,
    },
    to: {
      name: to.name,
      params: to.params,
      path: to.path,
    },
  });

  // Specifically handle navigation to create-event route
  if (to.name === "create-event") {
    console.log(
      "Router guard: navigating to create-event, clearing event data"
    );

    // Use the centralized clearEvent method from the store
    // This will handle clearing localStorage, sessionStorage, and store state
    const eventStore = useEventStore();
    eventStore.clearEvent();
  }

  next();
});

// Previous authentication code (commented out)
/*
router.beforeEach(async (to, from, next) => {
  try {
    // Check authentication status
    const isAuthenticated = await checkAuthentication()
    console.log(`Authentication check: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`)
    
    if (!isAuthenticated) {
      console.warn('Authentication check failed, redirecting to login')
      window.location.href = '/users/sign_in'
      return
    }
    
    // If we're authenticated, start the session keep-alive
    startSessionKeepAlive()
    
    // Handle navigation to tickets page
    if (to.name === 'ticket-creation') {
      // If eventId param exists, use it
      const eventId = to.params.eventId
      
      if (eventId) {
        console.log(`Navigating to tickets with event ID: ${eventId}`)
      } else {
        console.log('No event ID in route params for ticket-creation')
      }
    }
    
    next()
  } catch (error) {
    console.error('Error during navigation auth check:', error)
    next() // Still allow navigation in case of error
  }
})
*/

router.afterEach((to) => {
  actions.setCurrentRoute(to);
  actions.setNavigating(false);
  console.log(`Navigation complete: ${to.name}`, { params: to.params });
});

// Apply ticket navigation guard - note this is applied after router creation
// but will be initialized when the Vue app starts with Pinia available
ticketNavigationGuard(router);

// Create Context7 router store
export const routerStore = {
  state: readonly(state),
  ...actions,
  getCurrentRouteName: () => state.currentRoute?.name,
  getPreviousRouteName: () => state.previousRoute?.name,
  isActiveRoute: (name) => state.currentRoute?.name === name,
  isChildOfRoute: (parentName) => {
    return state.currentRoute?.matched.some(
      (record) => record.name === parentName
    );
  },
};

export { router, routes };
