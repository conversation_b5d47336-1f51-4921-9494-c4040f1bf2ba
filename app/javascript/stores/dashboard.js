import { defineStore } from "pinia";
import axios from "axios";

// Mock data for testing - enable by adding ?mock=true to URL in development
const MOCK_DATA_ENABLED = process.env.NODE_ENV === "development";

// Generate mock data for all attendees (comprehensive list)
const generateMockAllAttendees = (count = 100) => {
  const mockAttendees = [];
  const companies = [
    "Tech Corp",
    "Design Studio",
    "Marketing Agency",
    "Consulting Group",
    "Software Solutions",
    "Creative Labs",
    "Data Systems",
    "Innovation Hub",
  ];
  const statuses = [
    "confirmed",
    "unconfirmed",
    "cancelled",
    "declined",
    "payment_failed",
    "unpaid",
  ];
  const paymentStatuses = [0, 1, 2, 7]; // unpaid, part_paid, paid, payment_failed
  const paymentTypes = ["stripe", "bacs", "cash", "invoice"];

  for (let i = 1; i <= count; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const company = companies[Math.floor(Math.random() * companies.length)];
    const paymentStatus =
      paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
    const paymentType =
      paymentTypes[Math.floor(Math.random() * paymentTypes.length)];
    const hasBooking = status !== "unconfirmed";
    const bookingCount = hasBooking ? Math.floor(Math.random() * 3) + 1 : 0;
    const freeBooking = Math.random() > 0.8;

    // Determine status colors and icons
    let statusColor, statusIcon;
    switch (status) {
      case "confirmed":
        statusColor = "positive";
        statusIcon = "check_circle";
        break;
      case "unconfirmed":
        statusColor = "warning";
        statusIcon = "schedule";
        break;
      case "cancelled":
        statusColor = "negative";
        statusIcon = "cancel";
        break;
      case "declined":
        statusColor = "negative";
        statusIcon = "thumb_down";
        break;
      case "payment_failed":
        statusColor = "negative";
        statusIcon = "error";
        break;
      case "unpaid":
        statusColor = "warning";
        statusIcon = "payment";
        break;
      default:
        statusColor = "grey";
        statusIcon = "help";
    }

    mockAttendees.push({
      id: i,
      email: `attendee${i}@example.com`,
      forename: `John${i}`,
      surname: `Doe${i}`,
      company: company,
      phone: `+44 7700 ${String(i).padStart(6, "0")}`,
      job_title: `Position ${i}`,
      invite_sent: Math.random() > 0.3,
      user_invite_url: `https://example.com/invite/${i}`,
      declined: status === "declined",
      created_at: new Date(
        Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000
      ).toISOString(),
      updated_at: new Date(
        Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
      ).toISOString(),
      booked: hasBooking && status !== "cancelled",

      // Status information
      status: status,
      status_color: statusColor,
      status_icon: statusIcon,

      // Event booking details
      event_booking: hasBooking
        ? {
            id: 1000 + i,
            booking_count: bookingCount,
            booking_date: new Date(
              Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000
            ).toISOString(),
            payment_status: paymentStatus,
            payment_type: paymentType,
            free_booking: freeBooking,
            cancelled_at:
              status === "cancelled"
                ? new Date(
                    Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
                  ).toISOString()
                : null,
            cancelled_by: status === "cancelled" ? `Admin User ${i}` : null,
            payment_amount: freeBooking
              ? 0
              : (Math.random() * 500 + 50).toFixed(2),
            uuid: `uuid-${i}-${Date.now()}`,
          }
        : null,

      // Opt-out status
      opted_out: Math.random() > 0.9,
      opted_out_at:
        Math.random() > 0.9
          ? new Date(
              Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000
            ).toISOString()
          : null,

      // Additional fields for expanded functionality
      address1: `${i} Test Street`,
      address2: Math.random() > 0.7 ? `Apt ${i}` : null,
      town: "Test City",
      county: "Test County",
      postcode: `TE${i}T ${i}NG`,
      po_number: Math.random() > 0.8 ? `PO${String(i).padStart(6, "0")}` : null,
      user_type: 1, // attendee
      title: Math.random() > 0.5 ? "Mr" : "Ms",
    });
  }

  return mockAttendees;
};

const generateMockAttendees = (count = 50) => {
  const mockAttendees = [];
  for (let i = 1; i <= count; i++) {
    mockAttendees.push({
      id: i,
      email: `attendee${i}@example.com`,
      forename: `John${i}`,
      surname: `Doe${i}`,
      phone: `+44 7700 90${String(i).padStart(4, "0")}`,
      company: `Company ${i}`,
      job_title: `Position ${i}`,
      event_booking: {
        id: 1000 + i,
        booking_date: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
        payment_status: Math.random() > 0.8 ? "pending" : "paid",
        payment_type: Math.random() > 0.5 ? "stripe" : "bacs",
        free_booking: Math.random() > 0.9,
      },
      expanded: false,
    });
  }
  return mockAttendees;
};

const generateMockUnconfirmed = (count = 25) => {
  const mockUnconfirmed = [];
  for (let i = 1; i <= count; i++) {
    mockUnconfirmed.push({
      id: i + 1000,
      email: `unconfirmed${i}@example.com`,
      forename: `Jane${i}`,
      surname: `Smith${i}`,
      invited_at: new Date(
        Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
      ).toISOString(),
      declined: false,
    });
  }
  return mockUnconfirmed;
};

export const useDashboardStore = defineStore("dashboard", {
  state: () => ({
    event: {},
    attendees: [],
    unconfirmedAttendees: [],
    cancelledAttendees: [],
    declinedAttendees: [],
    failedPaymentAttendees: [],
    unpaidBookings: [],
    statistics: {
      totalAttendees: 0,
      totalConfirmed: 0,
      totalCancelled: 0,
      totalRevenue: 0,
      totalTicketsSold: 0,
    },
    loading: false,
    filters: {
      dateRange: null,
      searchTerm: "",
      ticketType: "all",
      status: "all",
      paymentStatus: "all",
    },
    pagination: {
      currentPage: 1,
      totalItems: 0,
      itemsPerPage: 20,
    },
    sort: {
      sortDirection: "asc",
      sortBy: "name",
    },
    exportStatus: null,
    exportUrl: null,
  }),

  actions: {
    setEvent(event) {
      this.event = event;
    },

    async fetchAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        if (MOCK_DATA_ENABLED) {
          // Simulate API delay
          await new Promise((resolve) => setTimeout(resolve, 500));

          const mockData = generateMockAttendees(75);
          const startIndex =
            (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
          const endIndex = startIndex + this.pagination.itemsPerPage;

          this.attendees = mockData.slice(startIndex, endIndex);
          this.pagination.totalItems = mockData.length;
          this.statistics.totalAttendees = mockData.length;
          this.statistics.totalConfirmed = mockData.length;
        } else {
          const response = await axios.get(
            `/registered_users/${this.event.id}/show_delegates_attending`,
            {
              params: {
                page: this.pagination.currentPage,
                searchTerm: this.filters.searchTerm,
                ticketTypeFilter: this.filters.ticketType,
                dateFromFilter: this.filters.dateRange?.from || null,
                dateToFilter: this.filters.dateRange?.to || null,
              },
            }
          );

          this.attendees = response.data.registered_users_confirmed || [];
          this.pagination.totalItems = response.data.total_count || 0;
          this.statistics.totalAttendees = response.data.total_count || 0;
          this.statistics.totalConfirmed = response.data.total_count || 0;
        }
      } catch (error) {
        console.error("Error fetching attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchUnconfirmedAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        if (MOCK_DATA_ENABLED) {
          await new Promise((resolve) => setTimeout(resolve, 300));

          const mockData = generateMockUnconfirmed(30);
          const startIndex =
            (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
          const endIndex = startIndex + this.pagination.itemsPerPage;

          this.unconfirmedAttendees = mockData.slice(startIndex, endIndex);
          this.pagination.totalItems = mockData.length;
        } else {
          const response = await axios.get(
            `/registered_users/${this.event.id}.json`,
            {
              params: {
                page: this.pagination.currentPage,
                attendeeFilter: this.filters.searchTerm || null,
              },
            }
          );

          this.unconfirmedAttendees = response.data.unconfirmed_users || [];
          this.pagination.totalItems = response.data.total_count || 0;
        }
      } catch (error) {
        console.error("Error fetching unconfirmed attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchCancelledAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_cancelled.json`,
          {
            params: {
              page: this.pagination.currentPage,
            },
          }
        );

        this.cancelledAttendees = response.data.cancelled_users || [];
        this.pagination.totalItems = response.data.total_count || 0;
        this.statistics.totalCancelled = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching cancelled attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchDeclinedAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_declined.json`,
          {
            params: {
              page: this.pagination.currentPage,
            },
          }
        );

        this.declinedAttendees = response.data.declined_users || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching declined attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchFailedPaymentAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_payments_failed`,
          {
            params: {
              page: this.pagination.currentPage,
              attendeeFilter: this.filters.searchTerm || null,
            },
          }
        );

        this.failedPaymentAttendees = response.data.event_bookings || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching failed payment attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchUnpaidBookings() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(`/unpaid_bookings/${this.event.id}`, {
          params: {
            page: this.pagination.currentPage,
          },
        });

        this.unpaidBookings = response.data.unpaid_bookings || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching unpaid bookings:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchAllAttendees(params = {}) {
      if (!this.event.id)
        return { users: [], total_count: 0, status_counts: {} };

      this.loading = true;
      try {
        if (MOCK_DATA_ENABLED) {
          // Simulate API delay
          await new Promise((resolve) => setTimeout(resolve, 500));

          let mockData = generateMockAllAttendees(150);

          // Apply search filter
          if (params.searchTerm) {
            const searchTerm = params.searchTerm.toLowerCase();
            mockData = mockData.filter(
              (attendee) =>
                attendee.forename.toLowerCase().includes(searchTerm) ||
                attendee.surname.toLowerCase().includes(searchTerm) ||
                attendee.email.toLowerCase().includes(searchTerm) ||
                attendee.company.toLowerCase().includes(searchTerm)
            );
          }

          // Apply status filter
          if (params.statusFilter && params.statusFilter !== "all") {
            mockData = mockData.filter(
              (attendee) => attendee.status === params.statusFilter
            );
          }

          // Apply payment status filter
          if (
            params.paymentStatusFilter &&
            params.paymentStatusFilter !== "all"
          ) {
            if (params.paymentStatusFilter === "free_booking") {
              mockData = mockData.filter(
                (attendee) => attendee.event_booking?.free_booking === true
              );
            } else {
              const statusMapping = {
                unpaid: 0,
                part_paid: 1,
                paid: 2,
                payment_failed: 7,
              };
              const statusInt = statusMapping[params.paymentStatusFilter];
              if (statusInt !== undefined) {
                mockData = mockData.filter(
                  (attendee) =>
                    attendee.event_booking?.payment_status === statusInt
                );
              }
            }
          }

          // Apply sorting
          if (params.sortBy) {
            const sortDirection = params.sortDirection === "desc" ? -1 : 1;
            mockData.sort((a, b) => {
              let aVal, bVal;
              switch (params.sortBy) {
                case "name":
                  aVal = `${a.forename} ${a.surname}`;
                  bVal = `${b.forename} ${b.surname}`;
                  break;
                case "email":
                  aVal = a.email;
                  bVal = b.email;
                  break;
                case "company":
                  aVal = a.company || "";
                  bVal = b.company || "";
                  break;
                case "booking_date":
                  aVal = a.event_booking?.booking_date || "";
                  bVal = b.event_booking?.booking_date || "";
                  break;
                case "payment_status":
                  aVal = a.event_booking?.payment_status || 0;
                  bVal = b.event_booking?.payment_status || 0;
                  break;
                case "booking_count":
                  aVal = a.event_booking?.booking_count || 0;
                  bVal = b.event_booking?.booking_count || 0;
                  break;
                default:
                  aVal = a.id;
                  bVal = b.id;
              }

              if (typeof aVal === "string") {
                return aVal.localeCompare(bVal) * sortDirection;
              }
              return (aVal - bVal) * sortDirection;
            });
          }

          // Calculate status counts
          const statusCounts = {
            confirmed: mockData.filter((a) => a.status === "confirmed").length,
            unconfirmed: mockData.filter((a) => a.status === "unconfirmed")
              .length,
            cancelled: mockData.filter((a) => a.status === "cancelled").length,
            declined: mockData.filter((a) => a.status === "declined").length,
            payment_failed: mockData.filter(
              (a) => a.status === "payment_failed"
            ).length,
            unpaid: mockData.filter((a) => a.status === "unpaid").length,
          };

          // Apply pagination
          const page = parseInt(params.page) || 1;
          const perPage = parseInt(params.per_page) || 25;
          const startIndex = (page - 1) * perPage;
          const endIndex = startIndex + perPage;
          const paginatedData = mockData.slice(startIndex, endIndex);

          return {
            users: paginatedData,
            total_count: mockData.length,
            user_count: mockData.length,
            current_page: page,
            per_page: perPage,
            status_counts: statusCounts,
          };
        } else {
          // Real API call
          const response = await axios.get(
            `/registered_users/${this.event.id}/show_all.json`,
            { params }
          );
          return response.data;
        }
      } catch (error) {
        console.error("Error fetching all attendees:", error);
        return { users: [], total_count: 0, status_counts: {} };
      } finally {
        this.loading = false;
      }
    },

    async fetchStatistics() {
      if (!this.event.id) return;

      try {
        const response = await axios.get(
          `/dashboard/${this.event.id}/confirmed_booking_data.json`
        );

        this.statistics = {
          totalAttendees: response.data.bookings_count || 0,
          totalConfirmed: response.data.bookings_count || 0,
          totalCancelled: 0, // This would need to be calculated separately
          totalRevenue: 0, // This would need to be calculated separately
          totalTicketsSold: response.data.bookings_count || 0,
        };
      } catch (error) {
        console.error("Error fetching statistics:", error);
      }
    },

    async exportAttendees(format = "csv") {
      if (!this.event.id) return;

      this.exportStatus = "processing";
      try {
        // Use the existing export endpoints from EventsController
        let exportUrl;
        if (format === "csv") {
          exportUrl = `/events/${this.event.id}/export_attendees.csv`;
        } else {
          exportUrl = `/events/${this.event.id}/export_bookings.csv`;
        }

        // For CSV exports, we can directly navigate to the URL
        window.open(exportUrl, "_blank");

        this.exportStatus = "completed";
        this.exportUrl = exportUrl;
        return exportUrl;
      } catch (error) {
        this.exportStatus = "failed";
        console.error("Error exporting attendees:", error);
        return null;
      }
    },

    /* async fetchAllAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        // Fetch all attendee types in parallel
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_all.json`,
          {
            params: {
              page: this.pagination.currentPage,
              searchTerm: this.filters.searchTerm,
              statusFilter: this.filters.status,
              paymentStatusFilter: this.filters.paymentStatus,
              sortBy: this.sort.sortBy,
              sortDirection: this.sort.sortDirection,
            },
          }
        );

        this.attendees = response.data.users || [];
        this.pagination.totalItems = response.data.total_count || 0;

        return response;
      } catch (error) {
        console.error("Error fetching all attendees:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },*/

    setPage(page) {
      this.pagination.currentPage = page;
    },

    setItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage;
      this.pagination.currentPage = 1; // Reset to first page when changing items per page
    },

    setSearchTerm(term) {
      this.filters.searchTerm = term;
      this.pagination.currentPage = 1; // Reset to first page when searching
    },

    setTicketTypeFilter(ticketType) {
      this.filters.ticketType = ticketType;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },

    setDateRangeFilter(dateRange) {
      this.filters.dateRange = dateRange;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },

    resetFilters() {
      this.filters = {
        dateRange: null,
        searchTerm: "",
        ticketType: "all",
      };
      this.pagination.currentPage = 1;
    },
  },

  getters: {
    getEvent: (state) => state.event,
    getAttendees: (state) => state.attendees,
    getUnconfirmedAttendees: (state) => state.unconfirmedAttendees,
    getCancelledAttendees: (state) => state.cancelledAttendees,
    getDeclinedAttendees: (state) => state.declinedAttendees,
    getFailedPaymentAttendees: (state) => state.failedPaymentAttendees,
    getUnpaidBookings: (state) => state.unpaidBookings,
    getStatistics: (state) => state.statistics,
    isLoading: (state) => state.loading,
    getCurrentPage: (state) => state.pagination.currentPage,
    getTotalItems: (state) => state.pagination.totalItems,
    getItemsPerPage: (state) => state.pagination.itemsPerPage,
    getTotalPages: (state) =>
      Math.ceil(state.pagination.totalItems / state.pagination.itemsPerPage),
    getExportStatus: (state) => state.exportStatus,
    getExportUrl: (state) => state.exportUrl,
  },
});
