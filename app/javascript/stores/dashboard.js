import { defineStore } from "pinia";
import axios from "axios";

// Mock data for testing - enable by adding ?mock=true to URL in development
const MOCK_DATA_ENABLED = process.env.NODE_ENV === "development";

const generateMockAttendees = (count = 50) => {
  const mockAttendees = [];
  for (let i = 1; i <= count; i++) {
    mockAttendees.push({
      id: i,
      email: `attendee${i}@example.com`,
      forename: `<PERSON>${i}`,
      surname: `<PERSON><PERSON>${i}`,
      phone: `+44 7700 90${String(i).padStart(4, "0")}`,
      company: `Company ${i}`,
      job_title: `Position ${i}`,
      event_booking: {
        id: 1000 + i,
        booking_date: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
        payment_status: Math.random() > 0.8 ? "pending" : "paid",
        payment_type: Math.random() > 0.5 ? "stripe" : "bacs",
        free_booking: Math.random() > 0.9,
      },
      expanded: false,
    });
  }
  return mockAttendees;
};

const generateMockUnconfirmed = (count = 25) => {
  const mockUnconfirmed = [];
  for (let i = 1; i <= count; i++) {
    mockUnconfirmed.push({
      id: i + 1000,
      email: `unconfirmed${i}@example.com`,
      forename: `Jane${i}`,
      surname: `Smith${i}`,
      invited_at: new Date(
        Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
      ).toISOString(),
      declined: false,
    });
  }
  return mockUnconfirmed;
};

export const useDashboardStore = defineStore("dashboard", {
  state: () => ({
    event: {},
    attendees: [],
    unconfirmedAttendees: [],
    cancelledAttendees: [],
    declinedAttendees: [],
    failedPaymentAttendees: [],
    unpaidBookings: [],
    statistics: {
      totalAttendees: 0,
      totalConfirmed: 0,
      totalCancelled: 0,
      totalRevenue: 0,
      totalTicketsSold: 0,
    },
    loading: false,
    filters: {
      dateRange: null,
      searchTerm: "",
      ticketType: "all",
    },
    pagination: {
      currentPage: 1,
      totalItems: 0,
      itemsPerPage: 20,
    },
    exportStatus: null,
    exportUrl: null,
  }),

  actions: {
    setEvent(event) {
      this.event = event;
    },

    async fetchAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        if (MOCK_DATA_ENABLED) {
          // Simulate API delay
          await new Promise((resolve) => setTimeout(resolve, 500));

          const mockData = generateMockAttendees(75);
          const startIndex =
            (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
          const endIndex = startIndex + this.pagination.itemsPerPage;

          this.attendees = mockData.slice(startIndex, endIndex);
          this.pagination.totalItems = mockData.length;
          this.statistics.totalAttendees = mockData.length;
          this.statistics.totalConfirmed = mockData.length;
        } else {
          const response = await axios.get(
            `/registered_users/${this.event.id}/show_delegates_attending`,
            {
              params: {
                page: this.pagination.currentPage,
                searchTerm: this.filters.searchTerm,
                ticketTypeFilter: this.filters.ticketType,
                dateFromFilter: this.filters.dateRange?.from || null,
                dateToFilter: this.filters.dateRange?.to || null,
              },
            }
          );

          this.attendees = response.data.registered_users_confirmed || [];
          this.pagination.totalItems = response.data.total_count || 0;
          this.statistics.totalAttendees = response.data.total_count || 0;
          this.statistics.totalConfirmed = response.data.total_count || 0;
        }
      } catch (error) {
        console.error("Error fetching attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchUnconfirmedAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        if (MOCK_DATA_ENABLED) {
          await new Promise((resolve) => setTimeout(resolve, 300));

          const mockData = generateMockUnconfirmed(30);
          const startIndex =
            (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
          const endIndex = startIndex + this.pagination.itemsPerPage;

          this.unconfirmedAttendees = mockData.slice(startIndex, endIndex);
          this.pagination.totalItems = mockData.length;
        } else {
          const response = await axios.get(
            `/registered_users/${this.event.id}.json`,
            {
              params: {
                page: this.pagination.currentPage,
                attendeeFilter: this.filters.searchTerm || null,
              },
            }
          );

          this.unconfirmedAttendees = response.data.unconfirmed_users || [];
          this.pagination.totalItems = response.data.total_count || 0;
        }
      } catch (error) {
        console.error("Error fetching unconfirmed attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchCancelledAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_cancelled.json`,
          {
            params: {
              page: this.pagination.currentPage,
            },
          }
        );

        this.cancelledAttendees = response.data.cancelled_users || [];
        this.pagination.totalItems = response.data.total_count || 0;
        this.statistics.totalCancelled = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching cancelled attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchDeclinedAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_declined.json`,
          {
            params: {
              page: this.pagination.currentPage,
            },
          }
        );

        this.declinedAttendees = response.data.declined_users || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching declined attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchFailedPaymentAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(
          `/registered_users/${this.event.id}/show_payments_failed`,
          {
            params: {
              page: this.pagination.currentPage,
              attendeeFilter: this.filters.searchTerm || null,
            },
          }
        );

        this.failedPaymentAttendees = response.data.event_bookings || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching failed payment attendees:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchUnpaidBookings() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        const response = await axios.get(`/unpaid_bookings/${this.event.id}`, {
          params: {
            page: this.pagination.currentPage,
          },
        });

        this.unpaidBookings = response.data.unpaid_bookings || [];
        this.pagination.totalItems = response.data.total_count || 0;
      } catch (error) {
        console.error("Error fetching unpaid bookings:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchStatistics() {
      if (!this.event.id) return;

      try {
        const response = await axios.get(
          `/dashboard/${this.event.id}/confirmed_booking_data.json`
        );

        this.statistics = {
          totalAttendees: response.data.bookings_count || 0,
          totalConfirmed: response.data.bookings_count || 0,
          totalCancelled: 0, // This would need to be calculated separately
          totalRevenue: 0, // This would need to be calculated separately
          totalTicketsSold: response.data.bookings_count || 0,
        };
      } catch (error) {
        console.error("Error fetching statistics:", error);
      }
    },

    async exportAttendees(format = "csv") {
      if (!this.event.id) return;

      this.exportStatus = "processing";
      try {
        // Use the existing export endpoints from EventsController
        let exportUrl;
        if (format === "csv") {
          exportUrl = `/events/${this.event.id}/export_attendees.csv`;
        } else {
          exportUrl = `/events/${this.event.id}/export_bookings.csv`;
        }

        // For CSV exports, we can directly navigate to the URL
        window.open(exportUrl, "_blank");

        this.exportStatus = "completed";
        this.exportUrl = exportUrl;
        return exportUrl;
      } catch (error) {
        this.exportStatus = "failed";
        console.error("Error exporting attendees:", error);
        return null;
      }
    },

    async fetchAllAttendees() {
      if (!this.event.id) return;

      this.loading = true;
      try {
        // Fetch all attendee types in parallel
        await Promise.all([
          this.fetchAttendees(),
          this.fetchUnconfirmedAttendees(),
          this.fetchCancelledAttendees(),
          this.fetchDeclinedAttendees(),
          this.fetchFailedPaymentAttendees(),
          this.fetchUnpaidBookings(),
        ]);

        // Combine all attendees with status information
        const allAttendees = [
          ...this.attendees.map((attendee) => ({
            ...attendee,
            status: "confirmed",
            statusColor: "positive",
            statusIcon: "check_circle",
          })),
          ...this.unconfirmedAttendees.map((attendee) => ({
            ...attendee,
            status: "unconfirmed",
            statusColor: "warning",
            statusIcon: "schedule",
          })),
          ...this.cancelledAttendees.map((attendee) => ({
            ...attendee,
            status: "cancelled",
            statusColor: "negative",
            statusIcon: "cancel",
          })),
          ...this.declinedAttendees.map((attendee) => ({
            ...attendee,
            status: "declined",
            statusColor: "negative",
            statusIcon: "thumb_down",
          })),
          ...this.failedPaymentAttendees.map((attendee) => ({
            ...attendee,
            status: "payment_failed",
            statusColor: "negative",
            statusIcon: "error",
          })),
          ...this.unpaidBookings.map((booking) => ({
            id: booking.id,
            email: booking.email,
            forename: booking.name?.split(" ")[0] || "",
            surname: booking.name?.split(" ").slice(1).join(" ") || "",
            event_booking: {
              id: booking.id,
              booking_date: booking.booking_date || booking.created_at,
            },
            status: "unpaid",
            statusColor: "warning",
            statusIcon: "payment",
          })),
        ];

        return allAttendees;
      } catch (error) {
        console.error("Error fetching all attendees:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    setPage(page) {
      this.pagination.currentPage = page;
    },

    setItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage;
      this.pagination.currentPage = 1; // Reset to first page when changing items per page
    },

    setSearchTerm(term) {
      this.filters.searchTerm = term;
      this.pagination.currentPage = 1; // Reset to first page when searching
    },

    setTicketTypeFilter(ticketType) {
      this.filters.ticketType = ticketType;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },

    setDateRangeFilter(dateRange) {
      this.filters.dateRange = dateRange;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },

    resetFilters() {
      this.filters = {
        dateRange: null,
        searchTerm: "",
        ticketType: "all",
      };
      this.pagination.currentPage = 1;
    },
  },

  getters: {
    getEvent: (state) => state.event,
    getAttendees: (state) => state.attendees,
    getUnconfirmedAttendees: (state) => state.unconfirmedAttendees,
    getCancelledAttendees: (state) => state.cancelledAttendees,
    getDeclinedAttendees: (state) => state.declinedAttendees,
    getFailedPaymentAttendees: (state) => state.failedPaymentAttendees,
    getUnpaidBookings: (state) => state.unpaidBookings,
    getStatistics: (state) => state.statistics,
    isLoading: (state) => state.loading,
    getCurrentPage: (state) => state.pagination.currentPage,
    getTotalItems: (state) => state.pagination.totalItems,
    getItemsPerPage: (state) => state.pagination.itemsPerPage,
    getTotalPages: (state) =>
      Math.ceil(state.pagination.totalItems / state.pagination.itemsPerPage),
    getExportStatus: (state) => state.exportStatus,
    getExportUrl: (state) => state.exportUrl,
  },
});
