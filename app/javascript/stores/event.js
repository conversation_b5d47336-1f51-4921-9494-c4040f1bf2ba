import { defineStore } from "pinia";
import axios from "axios";
import { readonly } from "vue";

// Mock data for testing event bookings
const MOCK_DATA_ENABLED = process.env.NODE_ENV === "development";

const generateMockEventBookings = (count = 50) => {
  const mockBookings = [];
  const paymentTypes = ["stripe", "bacs", "cash", "invoice"];
  const paymentStatuses = [
    "paid",
    "pending",
    "failed",
    "cancelled",
    "refunded",
  ];
  const companies = [
    "Tech Corp",
    "Design Studio",
    "Marketing Agency",
    "Consulting Group",
    "Software Solutions",
  ];

  for (let i = 1; i <= count; i++) {
    const paymentType =
      paymentTypes[Math.floor(Math.random() * paymentTypes.length)];
    const paymentStatus =
      paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
    const company = companies[Math.floor(Math.random() * companies.length)];

    mockBookings.push({
      id: 2000 + i,
      booking_reference: `BK${String(i).padStart(6, "0")}`,
      booking_date: new Date(
        Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000
      ).toISOString(),
      booking_count: Math.floor(Math.random() * 5) + 1,
      payment_type: paymentType,
      payment_status: paymentStatus,
      payment_amount: (Math.random() * 500 + 50).toFixed(2),
      currency: "GBP",
      free_booking: Math.random() > 0.8,
      cancelled_at:
        paymentStatus === "cancelled"
          ? new Date(
              Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
            ).toISOString()
          : null,

      // Booker details
      booker: {
        id: 3000 + i,
        email: `booker${i}@example.com`,
        forename: `Booker${i}`,
        surname: `Person${i}`,
        phone: `+44 7700 ${String(i).padStart(6, "0")}`,
        company: company,
        job_title: `Manager ${i}`,
        address1: `${i} Test Street`,
        city: "Test City",
        postcode: `TE${i}T ${i}NG`,
        country: "United Kingdom",
      },

      // Package bookings (tickets)
      package_bookings: [
        {
          id: 4000 + i,
          quantity_tickets: Math.floor(Math.random() * 3) + 1,
          package: {
            id: 5000 + (i % 5),
            title: `Ticket Type ${(i % 5) + 1}`,
            cost_b: (Math.random() * 200 + 25).toFixed(2),
            description: `Description for ticket type ${(i % 5) + 1}`,
          },
        },
      ],

      // Registered users (attendees for this booking)
      registered_users: Array.from(
        { length: Math.floor(Math.random() * 3) + 1 },
        (_, j) => ({
          id: 6000 + i * 10 + j,
          email: `attendee${i}_${j}@example.com`,
          forename: `Attendee${i}_${j}`,
          surname: `Person${i}_${j}`,
          phone: `+44 7800 ${String(i * 10 + j).padStart(6, "0")}`,
          company: company,
          job_title: `Position ${j + 1}`,
          dietary_requirements: Math.random() > 0.7 ? "Vegetarian" : null,
          accessibility_requirements:
            Math.random() > 0.9 ? "Wheelchair access" : null,
          attending_event:
            Math.random() > 0.3 ? new Date().toISOString() : null,
        })
      ),

      // Payment details
      booking_payments:
        paymentStatus === "paid"
          ? [
              {
                id: 7000 + i,
                amount: (Math.random() * 500 + 50).toFixed(2),
                currency: "GBP",
                payment_type: paymentType,
                payment_date: new Date(
                  Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
                ).toISOString(),
                stripe_payment_intent_id:
                  paymentType === "stripe" ? `pi_test_${i}` : null,
                net_amount: (Math.random() * 450 + 45).toFixed(2),
                stripe_fees:
                  paymentType === "stripe"
                    ? (Math.random() * 15 + 2).toFixed(2)
                    : 0,
                application_fees: (Math.random() * 10 + 1).toFixed(2),
                vat_amount: (Math.random() * 50 + 5).toFixed(2),
              },
            ]
          : [],

      // Timestamps
      created_at: new Date(
        Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000
      ).toISOString(),
      updated_at: new Date(
        Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
      ).toISOString(),
    });
  }

  return mockBookings;
};

export const useEventStore = defineStore("event", {
  state: () => ({
    // Dashboard-related state
    unconfirmedCount: 0,
    attendeesCount: 0,
    declinedCount: 0,
    cancelledCount: 0,
    failedPaymentCount: 0,
    eventBooking: null,
    selectedTickets: null,
    bookerRegResponses: [],
    bookerDetails: null,
    fees: null,
    bookingToken: null,
    importContactJobID: null,
    removeContactJobID: null,

    // Event-related state
    event: null,
    eventTypes: [],

    // Shared state
    isLoading: false,
    error: null,
    readOnly: false,
    chargeable: false,
    miniState: false,

    // Application state
    advancedUser: false,
    currentRoute: null,

    // Stepper navigation state
    visitedPages: [],
  }),

  getters: {
    // Dashboard getters
    getUnconfirmedCount: (state) => state.unconfirmedCount,
    getAttendeesCount: (state) => state.attendeesCount,
    getDeclinedCount: (state) => state.declinedCount,
    getCancelledCount: (state) => state.cancelledCount,
    getFailedPaymentCount: (state) => state.failedPaymentCount,
    getEventBooking: (state) => state.eventBooking,
    getBookerDetails: (state) => state.bookerDetails,
    getFees: (state) => state.fees,
    getBookingToken: (state) => state.bookingToken,
    getImportContactJobID: (state) => state.importContactJobID,
    getRemoveContactJobID: (state) => state.removeContactJobID,

    // Event getters
    getEvent: (state) => state.event,
    getEventId: (state) => state.event?.id,
    getEventTypes: (state) => state.eventTypes,
    getReadOnly: (state) => state.readOnly,
    getChargeable: (state) => state.chargeable,
    getMiniState: (state) => state.miniState,
    getVisitedPages: (state) => state.visitedPages || [],
    // Auto-loading getter that ensures event is loaded
    getEventWithAutoLoad: (state) => {
      return (eventId) => {
        // If no event ID provided, return current event or null
        if (!eventId) {
          return state.event;
        }

        // If event is already loaded and matches requested ID, return it
        if (state.event && state.event.id === Number(eventId)) {
          return state.event;
        }

        // Event not loaded or different ID - trigger loading
        // Note: This should be used with a computed property that watches for changes
        return null;
      };
    },
    getSafeEvent: (state) => {
      if (!state.event) {
        return {
          id: null,
          title: "",
          tickets: [],
          ticket_groups: [],
          event_address: {
            id: null,
            address1: "",
            address2: "",
            city: "",
            county: "",
            postcode: "",
            country_code: "",
          },
        };
      }
      const safeEvent = { ...state.event };
      if (!Array.isArray(safeEvent.tickets)) safeEvent.tickets = [];
      if (!Array.isArray(safeEvent.ticket_groups)) safeEvent.ticket_groups = [];
      if (!safeEvent.event_address) {
        safeEvent.event_address = {
          id: null,
          address1: "",
          address2: "",
          city: "",
          county: "",
          postcode: "",
          country_code: "",
        };
      }
      return safeEvent;
    },

    // Application getters
    isAdvancedUser: (state) => state.advancedUser,
  },

  actions: {
    // Dashboard actions
    setUnconfirmedCount(count) {
      this.unconfirmedCount = count;
    },
    setAttendeesCount(count) {
      this.attendeesCount = count;
    },
    setDeclinedCount(count) {
      this.declinedCount = count;
    },
    setCancelledCount(count) {
      this.cancelledCount = count;
    },
    setFailedPaymentCount(count) {
      this.failedPaymentCount = count;
    },
    setEventBooking(eventBooking) {
      this.eventBooking = eventBooking;
    },
    setBookerRegResponses(responses) {
      this.bookerRegResponses = responses;
    },
    setBookerDetails(bookerDetails) {
      if (bookerDetails) {
        bookerDetails.user_type = "booker";
      }
      this.bookerDetails = bookerDetails;
    },
    setFees(fees) {
      this.fees = fees;
    },
    setBookingToken(token) {
      this.bookingToken = token;
    },
    setImportContactJobID(job_id) {
      this.importContactJobID = job_id;
    },
    setRemoveContactJobID(job_id) {
      this.removeContactJobID = job_id;
    },
    setSelectedTickets(tickets) {
      this.selectedTickets = tickets;
    },

    // Shared state actions
    setReadOnly(val) {
      this.readOnly = val;
    },
    setChargeable(val) {
      this.chargeable = val;
    },

    // Event actions
    setEvent(event) {
      console.log("Setting event in store:", event);

      // Validation to ensure we have a proper event object
      if (!event || typeof event !== "object") {
        console.error("Invalid event object passed to store:", event);
        return;
      }

      // Ensure event has an ID
      if (!event.id) {
        console.warn("Event without ID was passed to store:", event);
      }

      // Store the event with proper reactivity - avoid deep copy which breaks reactivity
      // Only copy if absolutely necessary, otherwise maintain references for reactivity
      if (typeof event === "object" && event !== null) {
        this.event = { ...event };

        // Ensure arrays are properly reactive
        if (Array.isArray(event.registration_fields)) {
          this.event.registration_fields = [...event.registration_fields];
        }
        if (Array.isArray(event.tickets)) {
          this.event.tickets = [...event.tickets];
        }
        if (Array.isArray(event.ticket_groups)) {
          this.event.ticket_groups = [...event.ticket_groups];
        }
      } else {
        this.event = event;
      }

      console.log("Event after store update:", this.event);
    },
    setEventTypes(types) {
      this.eventTypes = types;
    },

    async fetchEvent(eventId) {
      try {
        const res = await axios.get(`/event_details/${eventId}`);
        return res;
      } catch (err) {
        console.log(err);
      }
    },
    async loadEvent(eventId, forceReload = false) {
      if (!eventId) {
        console.error("No event ID provided to loadEvent");
        return false;
      }
      if (
        this.getEventId === Number(eventId) &&
        !this.isLoading &&
        !forceReload
      ) {
        console.log(
          `Event ${eventId} is already loaded, skipping duplicate load`
        );
        return true;
      }

      if (forceReload) {
        console.log(`Force reloading event ${eventId} from database`);
        // Clear cached data for this event
        this.clearEvent();
      }
      this.isLoading = true;
      this.error = null;
      try {
        const cacheBuster = new Date().getTime();
        const response = await axios.get(
          `/event_details/${eventId}?_=${cacheBuster}`,
          {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );
        if (response.data.event) {
          this.setEvent(response.data.event);
          if (response.data.event.event_types) {
            this.setEventTypes(response.data.event.event_types);
          }

          // Store the current event ID in localStorage for persistence
          if (
            localStorage.getItem("hg-current-event-id") !== eventId.toString()
          ) {
            localStorage.setItem("hg-current-event-id", eventId.toString());
          }

          // Record when we last loaded the data
          localStorage.setItem("hg-event-last-loaded", Date.now().toString());

          // Automatically load tickets for immediate menu availability
          // This ensures hasEventAndTickets computed property works immediately
          console.log(
            `Auto-loading tickets for event ${eventId} to enable menu items`
          );
          await this.loadTickets(eventId);
          console.log(
            "Event after loadTickets in loadEvent:",
            this.event.tickets,
            this.event.ticket_groups
          );
          return true;
        } else {
          this.error = "Event data not found in response";
          return false;
        }
      } catch (error) {
        console.error(`Error loading event with ID: ${eventId}`, error);
        this.error = error.response?.data?.error || "Failed to load event";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async loadTickets(eventId) {
      if (!eventId) {
        console.error("No event ID provided to loadTickets");
        return false;
      }
      this.isLoading = true;
      this.error = null;
      try {
        const cacheBuster = new Date().getTime();
        const response = await axios.get(
          `/event_details/${eventId}/tickets?_=${cacheBuster}`,
          {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        if (response.data.tickets) {
          console.log("Raw tickets data from API:", response.data.tickets);
          console.log(
            "Raw ticket groups data from API:",
            response.data.ticket_groups
          );

          if (!this.event || this.event.id != eventId) {
            await this.loadEvent(eventId);
          }
          if (this.event) {
            this.event.tickets = response.data.tickets;
            this.event.ticket_groups = response.data.ticket_groups || [];
            console.log(
              "Tickets after assignment in loadTickets:",
              this.event.tickets
            );
            console.log(
              "Ticket groups after assignment in loadTickets:",
              this.event.ticket_groups
            );
            return true;
          } else {
            this.error = "No event object available to store tickets";
            return false;
          }
        } else {
          this.error = "Tickets data not found in response";
          return false;
        }
      } catch (error) {
        console.error(`Error loading tickets for event ID: ${eventId}`, error);
        this.error = error.response?.data?.error || "Failed to load tickets";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async loadEventBookings(ticket_id) {
      if (!this.event.id) return;

      this.isLoading = true;
      this.error = null;
      try {
        if (MOCK_DATA_ENABLED) {
          // Simulate API delay
          await new Promise((resolve) => setTimeout(resolve, 800));

          const mockBookings = generateMockEventBookings(75);
          const mockBookingData = {
            bookings: mockBookings,
            bookings_count: mockBookings.length,
            total_revenue: mockBookings
              .reduce(
                (sum, booking) => sum + parseFloat(booking.payment_amount || 0),
                0
              )
              .toFixed(2),
            paid_bookings: mockBookings.filter(
              (b) => b.payment_status === "paid"
            ).length,
            pending_bookings: mockBookings.filter(
              (b) => b.payment_status === "pending"
            ).length,
            failed_bookings: mockBookings.filter(
              (b) => b.payment_status === "failed"
            ).length,
            cancelled_bookings: mockBookings.filter(
              (b) => b.payment_status === "cancelled"
            ).length,
            total_attendees: mockBookings.reduce(
              (sum, booking) => sum + booking.registered_users.length,
              0
            ),
            payment_types: {
              stripe: mockBookings.filter((b) => b.payment_type === "stripe")
                .length,
              bacs: mockBookings.filter((b) => b.payment_type === "bacs")
                .length,
              cash: mockBookings.filter((b) => b.payment_type === "cash")
                .length,
              invoice: mockBookings.filter((b) => b.payment_type === "invoice")
                .length,
            },
          };

          this.setEventBooking(mockBookingData);
          return true;
        } else {
          const response = await axios.get(
            `/dashboard/${this.event.id}/confirmed_booking_data?ticket_id=${ticket_id}`
          );

          if (response.data.booking_data) {
            this.setEventBooking(response.data.booking_data);
            return true;
          } else {
            this.error = "Event data not found in response";
            return false;
          }
        }
      } catch (error) {
        console.error(
          `Error loading event bookings for ID: ${this.event.id}`,
          error
        );
        this.error =
          error.response?.data?.error || "Failed to load event bookings";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async toggleEventLive() {
      if (this.event) {
        try {
          if (!this.event.live) {
            await axios.put(`/events/${this.event.id}/change_live_status`, {
              step: this.event.step,
              is_public: this.event.is_public,
            });
            this.event.live = true;
            console.log(`Event ${this.event.id} is now offline`);
          } else {
            await axios.put(`/events/${this.event.id}/remove_live_status`);
            this.event.live = false;
            console.log(`Event ${this.event.id} is now live`);
          }
        } catch (error) {
          console.error(
            `Error toggling event live status for event ID: ${this.event.id}`,
            error
          );
          this.error =
            error.response?.data?.error || "Failed to toggle event live status";
        }
      } else {
        console.error("No event loaded to make live");
      }
    },
    async toggleEventPublic() {
      if (this.event) {
        try {
          await axios.put(`/events/${this.event.id}/change_public_status`, {
            is_public: !this.event.is_public,
          });

          this.event.is_public = !this.event.is_public;
          console.log(
            `Event ${this.event.id} is now ${
              this.event.is_public ? "public" : "private"
            }`
          );
        } catch (error) {
          console.error(
            `Error toggling event public status for event ID: ${this.event.id}`,
            error
          );
          this.error =
            error.response?.data?.error ||
            "Failed to toggle event public status";
        }
      } else {
        console.error("No event loaded to toggle public status");
      }
    },
    // Ensure event is loaded - automatically loads if not present
    async ensureEventLoaded(eventId) {
      if (!eventId) {
        console.error("No event ID provided to ensureEventLoaded");
        return false;
      }

      const numericEventId = Number(eventId);

      // Check if event is already loaded and matches
      if (this.event && this.event.id === numericEventId) {
        // Event is loaded but check if tickets are also loaded for menu availability
        const hasTicketsWithIds =
          this.event.tickets &&
          this.event.tickets.some((ticket) => ticket && ticket.id);
        const hasGroupTicketsWithIds =
          this.event.ticket_groups &&
          this.event.ticket_groups.some(
            (group) =>
              group.packages &&
              group.packages.some((ticket) => ticket && ticket.id)
          );

        if (!hasTicketsWithIds && !hasGroupTicketsWithIds) {
          console.log(
            `Event ${eventId} loaded but tickets missing, loading tickets for menu availability`
          );
          await this.loadTickets(eventId);
        } else {
          console.log(
            `Event ${eventId} already loaded with tickets, no need to reload`
          );
        }
        return true;
      }

      // Event not loaded or different - load it (which will also load tickets)
      console.log(`Auto-loading event ${eventId} as it's not currently loaded`);
      return await this.loadEvent(eventId);
    },
    clearEvent() {
      this.event = null;
      this.eventTypes = [];
      this.visitedPages = [];
      this.readOnly = false;
      this.chargeable = false;
      this.unconfirmedCount = 0;
      this.attendeesCount = 0;
      this.declinedCount = 0;
      this.cancelledCount = 0;
      this.failedPaymentCount = 0;
      this.tickets = [];
      this.ticketsLoaded = false;
      this.selectedTickets = [];
      this.bookerDetails = null;
      this.eventBooking = null;
      this.bookerRegResponses = [];
      this.fees = null;
      this.bookingToken = null;
      localStorage.removeItem("hg-event-store");
      localStorage.removeItem("hg-current-event-id");
      localStorage.removeItem("hg-event-last-loaded");
      localStorage.removeItem("hg-tickets-last-loaded");
      localStorage.removeItem("hg-force-ticket-reload");
      sessionStorage.removeItem("hg-current-event-id-backup");
    },

    async forceReloadEvent(eventId) {
      console.log(`🔄 Force reloading event ${eventId} from database`);
      return await this.loadEvent(eventId, true);
    },
    refreshState(event_id) {
      if (this.event?.id !== event_id) {
        this.importContactJobID = null;
        this.removeContactJobID = null;
      }
      this.eventBooking = null;
      this.selectedTickets = null;
      this.bookerRegResponses = [];
      this.fees = null;
      this.bookingToken = null;
    },

    setMiniState(value) {
      this.miniState = value;
    },

    // Application actions
    setAdvancedUser(value) {
      this.advancedUser = value;
    },
    setCurrentRoute(route) {
      this.currentRoute = route;
    },

    // Stepper navigation actions
    addVisitedPage(pageName) {
      if (!this.visitedPages.includes(pageName)) {
        this.visitedPages.push(pageName);
      }
    },

    async fetchEventBookingData(type, identifier, name = null) {
      this.isLoading = true;
      this.error = null;
      try {
        let response;
        if (type === "private") {
          response = await axios.get(`/invite/${identifier}.json`);
        } else if (type === "public") {
          response = await axios.get(`/event/${identifier}/${name}.json`);
        }
        return response.data;
      } catch (error) {
        console.error(
          `Error fetching event booking data for ${type} event:`,
          error
        );
        this.error =
          error.response?.data?.error || "Failed to load event booking data";
        throw error; // Re-throw to be handled by the component
      } finally {
        this.isLoading = false;
      }
    },
  },

  persist: {
    key: "hg-event-store",
    storage: localStorage,
    paths: [
      "event",
      "eventTypes",
      "advancedUser",
      "visitedPages",
      "chargeable",
      "readOnly",
    ],
    beforeRestore: (context) => {
      console.log("Restoring event store from localStorage");

      // Check if we're on the create-event route - if so, prevent restoration of event data
      const pathname = window.location.pathname;
      const hash = window.location.hash;

      // Enhanced detection for create-event related routes
      const isCreateEventRoute =
        pathname.includes("/events/new") ||
        pathname.includes("/create-event") ||
        hash.includes("/create-event") ||
        hash.includes("#/create") ||
        window.location.href.includes("create-event");

      if (isCreateEventRoute) {
        console.log("On create-event route, preventing event restoration");

        // Use the centralized clearEvent method via the store instance
        if (context.store.clearEvent) {
          // If the store is already initialized, use its method
          context.store.clearEvent();
        } else {
          // Otherwise, just return a clean state
          return {
            ...context.store.$state,
            event: null,
            eventTypes: [],
            visitedPages: [],
            unconfirmedCount: 0,
            attendeesCount: 0,
            declinedCount: 0,
            cancelledCount: 0,
            failedPaymentCount: 0,
            eventBooking: null,
            selectedTickets: null,
            bookerRegResponses: [],
            bookerDetails: null,
            fees: null,
            bookingToken: null,
          };
        }
      }
    },
    afterRestore: (context) => {
      console.log("Event store restored successfully");
      // Set a flag that we've restored from localStorage
      context.store.restoredFromStorage = true;

      // Validate restored event data to ensure we have tickets properly loaded
      if (context.store.event) {
        console.log(
          `Restored event #${context.store.event.id} with ${
            context.store.event.tickets?.length || 0
          } tickets`
        );

        // Ensure we have proper ticket arrays
        if (!Array.isArray(context.store.event.tickets)) {
          context.store.event.tickets = [];
          console.warn("Tickets array was missing, initialized empty array");
        } else if (context.store.event.tickets.length === 0) {
          console.warn(
            "Restored event has empty tickets array - will auto-load tickets on next access"
          );
        } else if (!context.store.event.tickets.some((t) => t && t.id)) {
          console.warn(
            "Restored tickets appear to be invalid (no IDs found) - will auto-load tickets on next access"
          );
          // Mark for reload by setting a flag
          localStorage.setItem("hg-force-ticket-reload", "true");
        } else {
          console.log(
            "Restored event has valid tickets with IDs - menu items should be available"
          );
        }

        if (!Array.isArray(context.store.event.ticket_groups)) {
          context.store.event.ticket_groups = [];
          console.warn(
            "Ticket groups array was missing, initialized empty array"
          );
        }

        // Set the event ID in localStorage for redundancy
        if (context.store.event.id) {
          localStorage.setItem("hg-current-event-id", context.store.event.id);
          localStorage.setItem("hg-event-last-loaded", Date.now().toString());
          // Also backup to sessionStorage as another redundancy layer
          sessionStorage.setItem(
            "hg-current-event-id-backup",
            context.store.event.id
          );
        }
      }
    },
  },
});
