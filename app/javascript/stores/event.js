import { defineStore } from "pinia";
import axios from "axios";
import { readonly } from "vue";

export const useEventStore = defineStore("event", {
  state: () => ({
    // Dashboard-related state
    unconfirmedCount: 0,
    attendeesCount: 0,
    declinedCount: 0,
    cancelledCount: 0,
    failedPaymentCount: 0,
    eventBooking: null,
    selectedTickets: null,
    bookerRegResponses: [],
    bookerDetails: null,
    fees: null,
    bookingToken: null,
    importContactJobID: null,
    removeContactJobID: null,

    // Event-related state
    event: null,
    eventTypes: [],

    // Shared state
    isLoading: false,
    error: null,
    readOnly: false,
    chargeable: false,
    miniState: false,

    // Application state
    advancedUser: false,
    currentRoute: null,

    // Stepper navigation state
    visitedPages: [],
  }),

  getters: {
    // Dashboard getters
    getUnconfirmedCount: (state) => state.unconfirmedCount,
    getAttendeesCount: (state) => state.attendeesCount,
    getDeclinedCount: (state) => state.declinedCount,
    getCancelledCount: (state) => state.cancelledCount,
    getFailedPaymentCount: (state) => state.failedPaymentCount,
    getEventBooking: (state) => state.eventBooking,
    getBookerDetails: (state) => state.bookerDetails,
    getFees: (state) => state.fees,
    getBookingToken: (state) => state.bookingToken,
    getImportContactJobID: (state) => state.importContactJobID,
    getRemoveContactJobID: (state) => state.removeContactJobID,

    // Event getters
    getEvent: (state) => state.event,
    getEventId: (state) => state.event?.id,
    getEventTypes: (state) => state.eventTypes,
    getReadOnly: (state) => state.readOnly,
    getChargeable: (state) => state.chargeable,
    getMiniState: (state) => state.miniState,
    getVisitedPages: (state) => state.visitedPages || [],
    // Auto-loading getter that ensures event is loaded
    getEventWithAutoLoad: (state) => {
      return (eventId) => {
        // If no event ID provided, return current event or null
        if (!eventId) {
          return state.event;
        }

        // If event is already loaded and matches requested ID, return it
        if (state.event && state.event.id === Number(eventId)) {
          return state.event;
        }

        // Event not loaded or different ID - trigger loading
        // Note: This should be used with a computed property that watches for changes
        return null;
      };
    },
    getSafeEvent: (state) => {
      if (!state.event) {
        return {
          id: null,
          title: "",
          tickets: [],
          ticket_groups: [],
          event_address: {
            id: null,
            address1: "",
            address2: "",
            city: "",
            county: "",
            postcode: "",
            country_code: "",
          },
        };
      }
      const safeEvent = { ...state.event };
      if (!Array.isArray(safeEvent.tickets)) safeEvent.tickets = [];
      if (!Array.isArray(safeEvent.ticket_groups)) safeEvent.ticket_groups = [];
      if (!safeEvent.event_address) {
        safeEvent.event_address = {
          id: null,
          address1: "",
          address2: "",
          city: "",
          county: "",
          postcode: "",
          country_code: "",
        };
      }
      return safeEvent;
    },

    // Application getters
    isAdvancedUser: (state) => state.advancedUser,
  },

  actions: {
    // Dashboard actions
    setUnconfirmedCount(count) {
      this.unconfirmedCount = count;
    },
    setAttendeesCount(count) {
      this.attendeesCount = count;
    },
    setDeclinedCount(count) {
      this.declinedCount = count;
    },
    setCancelledCount(count) {
      this.cancelledCount = count;
    },
    setFailedPaymentCount(count) {
      this.failedPaymentCount = count;
    },
    setEventBooking(eventBooking) {
      this.eventBooking = eventBooking;
    },
    setBookerRegResponses(responses) {
      this.bookerRegResponses = responses;
    },
    setBookerDetails(bookerDetails) {
      if (bookerDetails) {
        bookerDetails.user_type = "booker";
      }
      this.bookerDetails = bookerDetails;
    },
    setFees(fees) {
      this.fees = fees;
    },
    setBookingToken(token) {
      this.bookingToken = token;
    },
    setImportContactJobID(job_id) {
      this.importContactJobID = job_id;
    },
    setRemoveContactJobID(job_id) {
      this.removeContactJobID = job_id;
    },
    setSelectedTickets(tickets) {
      this.selectedTickets = tickets;
    },

    // Shared state actions
    setReadOnly(val) {
      this.readOnly = val;
    },
    setChargeable(val) {
      this.chargeable = val;
    },

    // Event actions
    setEvent(event) {
      console.log("Setting event in store:", event);

      // Validation to ensure we have a proper event object
      if (!event || typeof event !== "object") {
        console.error("Invalid event object passed to store:", event);
        return;
      }

      // Ensure event has an ID
      if (!event.id) {
        console.warn("Event without ID was passed to store:", event);
      }

      // Store the event with proper reactivity - avoid deep copy which breaks reactivity
      // Only copy if absolutely necessary, otherwise maintain references for reactivity
      if (typeof event === "object" && event !== null) {
        this.event = { ...event };

        // Ensure arrays are properly reactive
        if (Array.isArray(event.registration_fields)) {
          this.event.registration_fields = [...event.registration_fields];
        }
        if (Array.isArray(event.tickets)) {
          this.event.tickets = [...event.tickets];
        }
        if (Array.isArray(event.ticket_groups)) {
          this.event.ticket_groups = [...event.ticket_groups];
        }
      } else {
        this.event = event;
      }

      console.log("Event after store update:", this.event);
    },
    setEventTypes(types) {
      this.eventTypes = types;
    },

    async fetchEvent(eventId) {
      try {
        const res = await axios.get(`/event_details/${eventId}`);
        return res;
      } catch (err) {
        console.log(err);
      }
    },
    async loadEvent(eventId, forceReload = false) {
      if (!eventId) {
        console.error("No event ID provided to loadEvent");
        return false;
      }
      if (
        this.getEventId === Number(eventId) &&
        !this.isLoading &&
        !forceReload
      ) {
        console.log(
          `Event ${eventId} is already loaded, skipping duplicate load`
        );
        return true;
      }

      if (forceReload) {
        console.log(`Force reloading event ${eventId} from database`);
        // Clear cached data for this event
        this.clearEvent();
      }
      this.isLoading = true;
      this.error = null;
      try {
        const cacheBuster = new Date().getTime();
        const response = await axios.get(
          `/event_details/${eventId}?_=${cacheBuster}`,
          {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );
        if (response.data.event) {
          this.setEvent(response.data.event);
          if (response.data.event.event_types) {
            this.setEventTypes(response.data.event.event_types);
          }

          // Store the current event ID in localStorage for persistence
          if (
            localStorage.getItem("hg-current-event-id") !== eventId.toString()
          ) {
            localStorage.setItem("hg-current-event-id", eventId.toString());
          }

          // Record when we last loaded the data
          localStorage.setItem("hg-event-last-loaded", Date.now().toString());

          // Automatically load tickets for immediate menu availability
          // This ensures hasEventAndTickets computed property works immediately
          console.log(
            `Auto-loading tickets for event ${eventId} to enable menu items`
          );
          await this.loadTickets(eventId);
          console.log(
            "Event after loadTickets in loadEvent:",
            this.event.tickets,
            this.event.ticket_groups
          );
          return true;
        } else {
          this.error = "Event data not found in response";
          return false;
        }
      } catch (error) {
        console.error(`Error loading event with ID: ${eventId}`, error);
        this.error = error.response?.data?.error || "Failed to load event";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async loadTickets(eventId) {
      if (!eventId) {
        console.error("No event ID provided to loadTickets");
        return false;
      }
      this.isLoading = true;
      this.error = null;
      try {
        const cacheBuster = new Date().getTime();
        const response = await axios.get(
          `/event_details/${eventId}/tickets?_=${cacheBuster}`,
          {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        if (response.data.tickets) {
          console.log("Raw tickets data from API:", response.data.tickets);
          console.log(
            "Raw ticket groups data from API:",
            response.data.ticket_groups
          );

          if (!this.event || this.event.id != eventId) {
            await this.loadEvent(eventId);
          }
          if (this.event) {
            this.event.tickets = response.data.tickets;
            this.event.ticket_groups = response.data.ticket_groups || [];
            console.log(
              "Tickets after assignment in loadTickets:",
              this.event.tickets
            );
            console.log(
              "Ticket groups after assignment in loadTickets:",
              this.event.ticket_groups
            );
            return true;
          } else {
            this.error = "No event object available to store tickets";
            return false;
          }
        } else {
          this.error = "Tickets data not found in response";
          return false;
        }
      } catch (error) {
        console.error(`Error loading tickets for event ID: ${eventId}`, error);
        this.error = error.response?.data?.error || "Failed to load tickets";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async loadEventBookings() {
      if (!this.event.id) return;

      this.isLoading = true;
      this.error = null;
      try {
        const response = await axios.get(
          `/dashboard/${this.event.id}/confirmed_booking_data`
        );

        if (response.data.booking_data) {
          this.setEventBooking(response.data.booking_data);
          return true;
        } else {
          this.error = "Event data not found in response";
          return false;
        }
      } catch (error) {
        console.error(
          `Error loading event bookings for ID: ${this.event.id}`,
          error
        );
        this.error =
          error.response?.data?.error || "Failed to load event bookings";
        return false;
      } finally {
        this.isLoading = false;
      }
    },
    async toggleEventLive() {
      if (this.event) {
        try {
          if (!this.event.live) {
            await axios.put(`/events/${this.event.id}/change_live_status`, {
              step: this.event.step,
              is_public: this.event.is_public,
            });
            this.event.live = true;
            console.log(`Event ${this.event.id} is now offline`);
          } else {
            await axios.put(`/events/${this.event.id}/remove_live_status`);
            this.event.live = false;
            console.log(`Event ${this.event.id} is now live`);
          }
        } catch (error) {
          console.error(
            `Error toggling event live status for event ID: ${this.event.id}`,
            error
          );
          this.error =
            error.response?.data?.error || "Failed to toggle event live status";
        }
      } else {
        console.error("No event loaded to make live");
      }
    },
    async toggleEventPublic() {
      if (this.event) {
        try {
          await axios.put(`/events/${this.event.id}/change_public_status`, {
            is_public: !this.event.is_public,
          });

          this.event.is_public = !this.event.is_public;
          console.log(
            `Event ${this.event.id} is now ${
              this.event.is_public ? "public" : "private"
            }`
          );
        } catch (error) {
          console.error(
            `Error toggling event public status for event ID: ${this.event.id}`,
            error
          );
          this.error =
            error.response?.data?.error ||
            "Failed to toggle event public status";
        }
      } else {
        console.error("No event loaded to toggle public status");
      }
    },
    // Ensure event is loaded - automatically loads if not present
    async ensureEventLoaded(eventId) {
      if (!eventId) {
        console.error("No event ID provided to ensureEventLoaded");
        return false;
      }

      const numericEventId = Number(eventId);

      // Check if event is already loaded and matches
      if (this.event && this.event.id === numericEventId) {
        // Event is loaded but check if tickets are also loaded for menu availability
        const hasTicketsWithIds =
          this.event.tickets &&
          this.event.tickets.some((ticket) => ticket && ticket.id);
        const hasGroupTicketsWithIds =
          this.event.ticket_groups &&
          this.event.ticket_groups.some(
            (group) =>
              group.packages &&
              group.packages.some((ticket) => ticket && ticket.id)
          );

        if (!hasTicketsWithIds && !hasGroupTicketsWithIds) {
          console.log(
            `Event ${eventId} loaded but tickets missing, loading tickets for menu availability`
          );
          await this.loadTickets(eventId);
        } else {
          console.log(
            `Event ${eventId} already loaded with tickets, no need to reload`
          );
        }
        return true;
      }

      // Event not loaded or different - load it (which will also load tickets)
      console.log(`Auto-loading event ${eventId} as it's not currently loaded`);
      return await this.loadEvent(eventId);
    },
    clearEvent() {
      this.event = null;
      this.eventTypes = [];
      this.visitedPages = [];
      this.readOnly = false;
      this.chargeable = false;
      this.unconfirmedCount = 0;
      this.attendeesCount = 0;
      this.declinedCount = 0;
      this.cancelledCount = 0;
      this.failedPaymentCount = 0;
      this.tickets = [];
      this.ticketsLoaded = false;
      this.selectedTickets = [];
      this.bookerDetails = null;
      this.eventBooking = null;
      this.bookerRegResponses = [];
      this.fees = null;
      this.bookingToken = null;
      localStorage.removeItem("hg-event-store");
      localStorage.removeItem("hg-current-event-id");
      localStorage.removeItem("hg-event-last-loaded");
      localStorage.removeItem("hg-tickets-last-loaded");
      localStorage.removeItem("hg-force-ticket-reload");
      sessionStorage.removeItem("hg-current-event-id-backup");
    },

    async forceReloadEvent(eventId) {
      console.log(`🔄 Force reloading event ${eventId} from database`);
      return await this.loadEvent(eventId, true);
    },
    refreshState(event_id) {
      if (this.event?.id !== event_id) {
        this.importContactJobID = null;
        this.removeContactJobID = null;
      }
      this.eventBooking = null;
      this.selectedTickets = null;
      this.bookerRegResponses = [];
      this.fees = null;
      this.bookingToken = null;
    },

    setMiniState(value) {
      this.miniState = value;
    },

    // Application actions
    setAdvancedUser(value) {
      this.advancedUser = value;
    },
    setCurrentRoute(route) {
      this.currentRoute = route;
    },

    // Stepper navigation actions
    addVisitedPage(pageName) {
      if (!this.visitedPages.includes(pageName)) {
        this.visitedPages.push(pageName);
      }
    },

    async fetchEventBookingData(type, identifier, name = null) {
      this.isLoading = true;
      this.error = null;
      try {
        let response;
        if (type === "private") {
          response = await axios.get(`/invite/${identifier}.json`);
        } else if (type === "public") {
          response = await axios.get(`/event/${identifier}/${name}.json`);
        }
        return response.data;
      } catch (error) {
        console.error(
          `Error fetching event booking data for ${type} event:`,
          error
        );
        this.error =
          error.response?.data?.error || "Failed to load event booking data";
        throw error; // Re-throw to be handled by the component
      } finally {
        this.isLoading = false;
      }
    },
  },

  persist: {
    key: "hg-event-store",
    storage: localStorage,
    paths: [
      "event",
      "eventTypes",
      "advancedUser",
      "visitedPages",
      "chargeable",
      "readOnly",
    ],
    beforeRestore: (context) => {
      console.log("Restoring event store from localStorage");

      // Check if we're on the create-event route - if so, prevent restoration of event data
      const pathname = window.location.pathname;
      const hash = window.location.hash;

      // Enhanced detection for create-event related routes
      const isCreateEventRoute =
        pathname.includes("/events/new") ||
        pathname.includes("/create-event") ||
        hash.includes("/create-event") ||
        hash.includes("#/create") ||
        window.location.href.includes("create-event");

      if (isCreateEventRoute) {
        console.log("On create-event route, preventing event restoration");

        // Use the centralized clearEvent method via the store instance
        if (context.store.clearEvent) {
          // If the store is already initialized, use its method
          context.store.clearEvent();
        } else {
          // Otherwise, just return a clean state
          return {
            ...context.store.$state,
            event: null,
            eventTypes: [],
            visitedPages: [],
            unconfirmedCount: 0,
            attendeesCount: 0,
            declinedCount: 0,
            cancelledCount: 0,
            failedPaymentCount: 0,
            eventBooking: null,
            selectedTickets: null,
            bookerRegResponses: [],
            bookerDetails: null,
            fees: null,
            bookingToken: null,
          };
        }
      }
    },
    afterRestore: (context) => {
      console.log("Event store restored successfully");
      // Set a flag that we've restored from localStorage
      context.store.restoredFromStorage = true;

      // Validate restored event data to ensure we have tickets properly loaded
      if (context.store.event) {
        console.log(
          `Restored event #${context.store.event.id} with ${
            context.store.event.tickets?.length || 0
          } tickets`
        );

        // Ensure we have proper ticket arrays
        if (!Array.isArray(context.store.event.tickets)) {
          context.store.event.tickets = [];
          console.warn("Tickets array was missing, initialized empty array");
        } else if (context.store.event.tickets.length === 0) {
          console.warn(
            "Restored event has empty tickets array - will auto-load tickets on next access"
          );
        } else if (!context.store.event.tickets.some((t) => t && t.id)) {
          console.warn(
            "Restored tickets appear to be invalid (no IDs found) - will auto-load tickets on next access"
          );
          // Mark for reload by setting a flag
          localStorage.setItem("hg-force-ticket-reload", "true");
        } else {
          console.log(
            "Restored event has valid tickets with IDs - menu items should be available"
          );
        }

        if (!Array.isArray(context.store.event.ticket_groups)) {
          context.store.event.ticket_groups = [];
          console.warn(
            "Ticket groups array was missing, initialized empty array"
          );
        }

        // Set the event ID in localStorage for redundancy
        if (context.store.event.id) {
          localStorage.setItem("hg-current-event-id", context.store.event.id);
          localStorage.setItem("hg-event-last-loaded", Date.now().toString());
          // Also backup to sessionStorage as another redundancy layer
          sessionStorage.setItem(
            "hg-current-event-id-backup",
            context.store.event.id
          );
        }
      }
    },
  },
});
