import { createRouter, createWebHashHistory } from "vue-router";

// Import components
import DashMain from "./components/DashMain.vue";
import DelegatesAttending from "./components/DelegatesAttending.vue";
import DelegatesUnconfirmed from "./components/DelegatesUnconfirmed.vue";
import DelegatesCancelled from "./components/DelegatesCancelled.vue";
import DelegatesDeclined from "./components/DelegatesDeclined.vue";
import DelegatesFailedPayment from "./components/DelegatesFailedPayment.vue";
import BookingsUnpaid from "./components/BookingsUnpaid.vue";
import AmendBookings from "./components/AmendBookings.vue";
import Payments from "./components/Payments.vue";
import DownloadPoller from "./components/DownloadPoller.vue";
import EventBookingsTable from "./components/EventBookingsTable.vue";

// Import dependencies from other modules
import TermsVue from "@/events/components/terms/Terms.vue";
import EventPreview from "@/events/components/preview/EventPreview.vue";

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "",
      component: DashMain,
      name: "dash-main",
    },
    {
      path: "/attendees",
      component: DelegatesAttending,
      name: "attendees",
    },
    {
      path: "/attendees-unconfirmed",
      component: DelegatesUnconfirmed,
      name: "attendees-unconfirmed",
    },
    {
      path: "/attendees-payment-failed",
      component: DelegatesFailedPayment,
      name: "attendees-payment-failed",
    },

    {
      path: "/bookings-unpaid",
      component: BookingsUnpaid,
      name: "bookings-unpaid",
    },

    {
      path: "/attendees-cancelled",
      component: DelegatesCancelled,
      name: "attendees-cancelled",
    },
    {
      path: "/attendees-declined",
      component: DelegatesDeclined,
      name: "attendees-declined",
    },
    {
      path: "/payments/:status",
      component: Payments,
      name: "payments",
    },
    {
      path: "/event-bookings",
      component: EventBookingsTable,
      name: "event-bookings",
    },
    {
      path: "/terms",
      component: TermsVue,
      props: { noredirect: true },
      name: "terms",
    },
    {
      path: "/amend-bookings/:bookingId",
      name: "amend-bookings",
      component: AmendBookings,
      props: true,
    },

    {
      path: "/preview",
      name: "preview",
      component: EventPreview,
    },
  ],
});

export default router;
