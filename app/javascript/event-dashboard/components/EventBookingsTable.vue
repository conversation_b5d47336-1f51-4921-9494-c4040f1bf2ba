<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="text-h6 q-mb-xs">Event Bookings</div>
    <div class="text-subtitle2 text-grey q-mb-md">
      View and manage all bookings for your event
    </div>

    <!-- Statistics Cards -->
    <div class="row q-gutter-md q-mb-lg" v-if="eventBooking">
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-primary">{{ eventBooking.bookings_count || 0 }}</div>
          <div class="text-caption">Total Bookings</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-positive">{{ eventBooking.paid_bookings || 0 }}</div>
          <div class="text-caption">Paid</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-warning">{{ eventBooking.pending_bookings || 0 }}</div>
          <div class="text-caption">Pending</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">{{ eventBooking.failed_bookings || 0 }}</div>
          <div class="text-caption">Failed</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-info">{{ eventBooking.total_attendees || 0 }}</div>
          <div class="text-caption">Attendees</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-green">£{{ eventBooking.total_revenue || '0.00' }}</div>
          <div class="text-caption">Revenue</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Controls -->
    <div class="row q-gutter-md q-mb-lg">
      <q-input
        v-model="searchTerm"
        placeholder="Search bookings..."
        class="col-md-4 col-sm-6 col-xs-12"
        @update:model-value="onSearch"
        debounce="300"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <q-select
        v-model="statusFilter"
        :options="statusOptions"
        label="Filter by Payment Status"
        class="col-md-3 col-sm-4 col-xs-12"
        @update:model-value="onFilterChange"
        clearable
      />

      <q-btn
        color="primary"
        icon="refresh"
        label="Refresh"
        @click="refreshData"
        :loading="loading"
      />
    </div>

    <!-- Bookings Table -->
    <q-table
      :rows="filteredBookings"
      :columns="columns"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      @request="onRequest"
      binary-state-sort
      flat
      bordered
    >
      <!-- Booking Reference Column -->
      <template v-slot:body-cell-booking_reference="props">
        <q-td :props="props">
          <div class="text-weight-medium">
            {{ props.row.booking_reference }}
          </div>
        </q-td>
      </template>

      <!-- Booker Column -->
      <template v-slot:body-cell-booker="props">
        <q-td :props="props">
          <div>
            <div class="text-weight-medium">
              {{ props.row.booker.forename }} {{ props.row.booker.surname }}
            </div>
            <div class="text-caption text-grey">{{ props.row.booker.email }}</div>
          </div>
        </q-td>
      </template>

      <!-- Payment Status Column -->
      <template v-slot:body-cell-payment_status="props">
        <q-td :props="props">
          <q-badge
            :color="getPaymentStatusColor(props.row.payment_status)"
            :label="getPaymentStatusLabel(props.row.payment_status)"
          />
        </q-td>
      </template>

      <!-- Payment Amount Column -->
      <template v-slot:body-cell-payment_amount="props">
        <q-td :props="props">
          <div class="text-weight-medium">
            £{{ props.row.payment_amount }}
          </div>
        </q-td>
      </template>

      <!-- Attendees Column -->
      <template v-slot:body-cell-attendees="props">
        <q-td :props="props">
          <q-chip
            color="info"
            text-color="white"
            :label="props.row.registered_users.length"
            size="sm"
          />
        </q-td>
      </template>

      <!-- Booking Date Column -->
      <template v-slot:body-cell-booking_date="props">
        <q-td :props="props">
          {{ formatDate(props.row.booking_date) }}
        </q-td>
      </template>

      <!-- Actions Column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn-dropdown
            color="primary"
            icon="more_vert"
            size="sm"
            flat
            dense
          >
            <q-list>
              <q-item clickable v-close-popup @click="viewBookingDetails(props.row)">
                <q-item-section avatar>
                  <q-icon name="visibility" />
                </q-item-section>
                <q-item-section>View Details</q-item-section>
              </q-item>
              
              <q-item clickable v-close-popup @click="viewAttendees(props.row)">
                <q-item-section avatar>
                  <q-icon name="people" />
                </q-item-section>
                <q-item-section>View Attendees</q-item-section>
              </q-item>
              
              <q-item 
                v-if="props.row.payment_status === 'pending'"
                clickable 
                v-close-popup 
                @click="sendPaymentReminder(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="send" />
                </q-item-section>
                <q-item-section>Send Payment Reminder</q-item-section>
              </q-item>
              
              <q-item 
                v-if="['paid', 'pending'].includes(props.row.payment_status)"
                clickable 
                v-close-popup 
                @click="cancelBooking(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="cancel" />
                </q-item-section>
                <q-item-section>Cancel Booking</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </q-td>
      </template>
    </q-table>

    <TestDataHelper v-if="isDevelopment" />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import { format } from "date-fns";
import TestDataHelper from "./TestDataHelper.vue";

const $q = useQuasar();
const eventStore = useEventStore();

// Reactive data
const searchTerm = ref("");
const statusFilter = ref(null);
const isDevelopment = process.env.NODE_ENV === "development";

// Table columns configuration
const columns = [
  {
    name: "booking_reference",
    label: "Booking Ref",
    field: "booking_reference",
    align: "left",
    sortable: true,
  },
  {
    name: "booker",
    label: "Booker",
    field: (row) => `${row.booker.forename} ${row.booker.surname}`,
    align: "left",
    sortable: true,
  },
  {
    name: "payment_status",
    label: "Payment Status",
    field: "payment_status",
    align: "left",
    sortable: true,
  },
  {
    name: "payment_amount",
    label: "Amount",
    field: "payment_amount",
    align: "right",
    sortable: true,
  },
  {
    name: "attendees",
    label: "Attendees",
    field: (row) => row.registered_users.length,
    align: "center",
    sortable: true,
  },
  {
    name: "booking_date",
    label: "Booking Date",
    field: "booking_date",
    align: "left",
    sortable: true,
  },
  {
    name: "actions",
    label: "Actions",
    field: "actions",
    align: "center",
    sortable: false,
  },
];

// Status filter options
const statusOptions = [
  { label: "Paid", value: "paid" },
  { label: "Pending", value: "pending" },
  { label: "Failed", value: "failed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Refunded", value: "refunded" },
];

// Table pagination
const pagination = ref({
  sortBy: "booking_date",
  descending: true,
  page: 1,
  rowsPerPage: 25,
  rowsNumber: 0,
});

// Computed properties
const loading = computed(() => eventStore.isLoading);
const eventBooking = computed(() => eventStore.getEventBooking);

const filteredBookings = computed(() => {
  if (!eventBooking.value?.bookings) return [];
  
  let filtered = eventBooking.value.bookings;

  // Filter by search term
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase();
    filtered = filtered.filter(
      (booking) =>
        booking.booking_reference?.toLowerCase().includes(search) ||
        booking.booker.forename?.toLowerCase().includes(search) ||
        booking.booker.surname?.toLowerCase().includes(search) ||
        booking.booker.email?.toLowerCase().includes(search) ||
        booking.booker.company?.toLowerCase().includes(search)
    );
  }

  // Filter by status
  if (statusFilter.value) {
    filtered = filtered.filter(
      (booking) => booking.payment_status === statusFilter.value
    );
  }

  return filtered;
});

// Methods
const refreshData = async () => {
  try {
    // Set mock event if in development and no event exists
    if (isDevelopment && !eventStore.event?.id) {
      eventStore.setEvent({
        id: 999,
        title: "Test Event",
        description: "Test event for development",
      });
    }

    await eventStore.loadEventBookings();
    pagination.value.rowsNumber = filteredBookings.value.length;
  } catch (error) {
    console.error("Error refreshing data:", error);
    $q.notify({
      message: "Failed to refresh booking data",
      color: "negative",
      icon: "error",
    });
  }
};

const onSearch = () => {
  pagination.value.page = 1;
};

const onFilterChange = () => {
  pagination.value.page = 1;
};

const onRequest = (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return "-";
  try {
    return format(new Date(dateString), "MMM dd, yyyy HH:mm");
  } catch (error) {
    return "-";
  }
};

const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    paid: "positive",
    pending: "warning",
    failed: "negative",
    cancelled: "negative",
    refunded: "info",
  };
  return colors[paymentStatus] || "grey";
};

const getPaymentStatusLabel = (paymentStatus) => {
  const labels = {
    paid: "Paid",
    pending: "Pending",
    failed: "Failed",
    cancelled: "Cancelled",
    refunded: "Refunded",
  };
  return labels[paymentStatus] || paymentStatus;
};

// Action methods
const viewBookingDetails = (booking) => {
  const attendeesList = booking.registered_users.map(u => `${u.forename} ${u.surname}`).join(', ');
  
  $q.dialog({
    title: "Booking Details",
    message: `
      <div><strong>Booking Reference:</strong> ${booking.booking_reference}</div>
      <div><strong>Booker:</strong> ${booking.booker.forename} ${booking.booker.surname}</div>
      <div><strong>Email:</strong> ${booking.booker.email}</div>
      <div><strong>Company:</strong> ${booking.booker.company || "N/A"}</div>
      <div><strong>Payment Status:</strong> ${getPaymentStatusLabel(booking.payment_status)}</div>
      <div><strong>Amount:</strong> £${booking.payment_amount}</div>
      <div><strong>Attendees:</strong> ${attendeesList}</div>
    `,
    html: true,
  });
};

const viewAttendees = (booking) => {
  const attendeesList = booking.registered_users.map(u => 
    `<div>• ${u.forename} ${u.surname} (${u.email})</div>`
  ).join('');
  
  $q.dialog({
    title: "Booking Attendees",
    message: `
      <div><strong>Booking:</strong> ${booking.booking_reference}</div>
      <div class="q-mt-md"><strong>Attendees:</strong></div>
      ${attendeesList}
    `,
    html: true,
  });
};

const sendPaymentReminder = (booking) => {
  $q.dialog({
    title: "Send Payment Reminder",
    message: `Send payment reminder to ${booking.booker.forename} ${booking.booker.surname}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    $q.notify({
      message: "Payment reminder sent successfully",
      color: "positive",
      icon: "send",
    });
  });
};

const cancelBooking = (booking) => {
  $q.dialog({
    title: "Cancel Booking",
    message: `Cancel booking ${booking.booking_reference}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    $q.notify({
      message: "Booking cancelled successfully",
      color: "positive",
      icon: "cancel",
    });
  });
};

// Lifecycle
onMounted(() => {
  refreshData();
});
</script>
