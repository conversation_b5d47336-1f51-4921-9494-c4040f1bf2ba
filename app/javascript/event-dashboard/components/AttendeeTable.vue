<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="text-h6 q-mb-xs">All Attendees</div>
    <div class="text-subtitle2 text-grey q-mb-md">
      Manage all attendees for your event across all statuses
    </div>

    <!-- Filters and Search -->
    <div class="row q-gutter-md q-mb-lg">
      <q-input
        v-model="searchTerm"
        borderless
        placeholder="Search attendees..."
        class="col-md-4 col-sm-6 col-xs-12"
        @update:model-value="onSearch"
        debounce="300"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <q-select
        v-model="statusFilter"
        :options="statusOptions"
        label="Filter by Status"
        class="col-md-3 col-sm-4 col-xs-12"
        @update:model-value="onFilterChange"
        clearable
      />

      <q-btn
        color="primary"
        icon="refresh"
        label="Refresh"
        @click="refreshData"
        :loading="loading"
      />
    </div>

    <!-- Statistics Cards -->
    <div class="row q-gutter-md q-mb-lg">
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-positive">{{ statusCounts.confirmed }}</div>
          <div class="text-caption">Confirmed</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-warning">{{ statusCounts.unconfirmed }}</div>
          <div class="text-caption">Unconfirmed</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">{{ statusCounts.cancelled }}</div>
          <div class="text-caption">Cancelled</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">{{ statusCounts.declined }}</div>
          <div class="text-caption">Declined</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-warning">{{ statusCounts.unpaid }}</div>
          <div class="text-caption">Unpaid</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Main Attendees Table -->
    <q-table
      :rows="filteredAttendees"
      :columns="columns"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      @request="onRequest"
      binary-state-sort
      flat
      bordered
    >
      <!-- Status Column -->
      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-badge
            :color="props.row.statusColor"
            :icon="props.row.statusIcon"
            :label="getStatusLabel(props.row.status)"
          />
        </q-td>
      </template>

      <!-- Name Column -->
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div class="text-weight-medium">
            {{ props.row.forename }} {{ props.row.surname }}
          </div>
        </q-td>
      </template>

      <!-- Booking Date Column -->
      <template v-slot:body-cell-booking_date="props">
        <q-td :props="props">
          {{ formatDate(props.row.event_booking?.booking_date) }}
        </q-td>
      </template>

      <!-- Payment Status Column -->
      <template v-slot:body-cell-payment_status="props">
        <q-td :props="props">
          <q-chip
            v-if="props.row.event_booking?.payment_status"
            :color="
              getPaymentStatusColor(props.row.event_booking.payment_status)
            "
            text-color="white"
            size="sm"
          >
            {{ getPaymentStatusLabel(props.row.event_booking.payment_status) }}
          </q-chip>
          <span v-else class="text-grey">-</span>
        </q-td>
      </template>

      <!-- Actions Column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn-dropdown color="primary" icon="more_vert" size="sm" flat dense>
            <q-list>
              <q-item clickable v-close-popup @click="viewDetails(props.row)">
                <q-item-section avatar>
                  <q-icon name="visibility" />
                </q-item-section>
                <q-item-section>View Details</q-item-section>
              </q-item>

              <q-item
                v-if="props.row.status === 'unconfirmed'"
                clickable
                v-close-popup
                @click="resendInvite(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="send" />
                </q-item-section>
                <q-item-section>Resend Invite</q-item-section>
              </q-item>

              <q-item
                v-if="['confirmed', 'unconfirmed'].includes(props.row.status)"
                clickable
                v-close-popup
                @click="cancelBooking(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="cancel" />
                </q-item-section>
                <q-item-section>Cancel Booking</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </q-td>
      </template>
    </q-table>

    <TestDataHelper v-if="isDevelopment" />
  </q-page>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { useDashboardStore } from "@/stores/dashboard";
import { useQuasar } from "quasar";
import { format } from "date-fns";
import TestDataHelper from "./TestDataHelper.vue";

const $q = useQuasar();
const dashboardStore = useDashboardStore();

// Reactive data
const allAttendees = ref([]);
const searchTerm = ref("");
const statusFilter = ref(null);
const isDevelopment = process.env.NODE_ENV === "development";

// Table columns configuration
const columns = [
  {
    name: "status",
    label: "Status",
    field: "status",
    align: "left",
    sortable: true,
  },
  {
    name: "name",
    label: "Name",
    field: (row) => `${row.forename} ${row.surname}`,
    align: "left",
    sortable: true,
  },
  {
    name: "email",
    label: "Email",
    field: "email",
    align: "left",
    sortable: true,
  },
  {
    name: "booking_id",
    label: "Booking ID",
    field: (row) => row.event_booking?.id || "-",
    align: "left",
    sortable: true,
  },
  {
    name: "booking_date",
    label: "Booking Date",
    field: (row) => row.event_booking?.booking_date,
    align: "left",
    sortable: true,
  },
  {
    name: "payment_status",
    label: "Payment",
    field: (row) => row.event_booking?.payment_status,
    align: "left",
    sortable: true,
  },
  {
    name: "company",
    label: "Company",
    field: "company",
    align: "left",
    sortable: true,
  },
  {
    name: "actions",
    label: "Actions",
    field: "actions",
    align: "center",
    sortable: false,
  },
];

// Status filter options
const statusOptions = [
  { label: "Confirmed", value: "confirmed" },
  { label: "Unconfirmed", value: "unconfirmed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Declined", value: "declined" },
  { label: "Payment Failed", value: "payment_failed" },
  { label: "Unpaid", value: "unpaid" },
];

// Table pagination
const pagination = ref({
  sortBy: "name",
  descending: false,
  page: 1,
  rowsPerPage: 25,
  rowsNumber: 0,
});

// Computed properties
const loading = computed(() => dashboardStore.isLoading);

const filteredAttendees = computed(() => {
  let filtered = allAttendees.value;

  // Filter by search term
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase();
    filtered = filtered.filter(
      (attendee) =>
        attendee.forename?.toLowerCase().includes(search) ||
        attendee.surname?.toLowerCase().includes(search) ||
        attendee.email?.toLowerCase().includes(search) ||
        attendee.company?.toLowerCase().includes(search)
    );
  }

  // Filter by status
  if (statusFilter.value) {
    filtered = filtered.filter(
      (attendee) => attendee.status === statusFilter.value
    );
  }

  return filtered;
});

const statusCounts = computed(() => {
  const counts = {
    confirmed: 0,
    unconfirmed: 0,
    cancelled: 0,
    declined: 0,
    payment_failed: 0,
    unpaid: 0,
  };

  allAttendees.value.forEach((attendee) => {
    if (counts.hasOwnProperty(attendee.status)) {
      counts[attendee.status]++;
    }
  });

  return counts;
});

// Methods
const refreshData = async () => {
  try {
    // Set mock event if in development and no event exists
    if (isDevelopment && !dashboardStore.event.id) {
      dashboardStore.setEvent({
        id: 999,
        title: "Test Event",
        description: "Test event for development",
      });
    }

    allAttendees.value = await dashboardStore.fetchAllAttendees();
    pagination.value.rowsNumber = allAttendees.value.length;
  } catch (error) {
    console.error("Error refreshing data:", error);
    $q.notify({
      message: "Failed to refresh attendee data",
      color: "negative",
      icon: "error",
    });
  }
};

const onSearch = () => {
  // Search is handled by computed property
  pagination.value.page = 1;
};

const onFilterChange = () => {
  // Filter is handled by computed property
  pagination.value.page = 1;
};

const onRequest = (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return "-";
  try {
    return format(new Date(dateString), "MMM dd, yyyy HH:mm");
  } catch (error) {
    return "-";
  }
};

const getStatusLabel = (status) => {
  const labels = {
    confirmed: "Confirmed",
    unconfirmed: "Unconfirmed",
    cancelled: "Cancelled",
    declined: "Declined",
    payment_failed: "Payment Failed",
    unpaid: "Unpaid",
  };
  return labels[status] || status;
};

const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    paid: "positive",
    pending: "warning",
    failed: "negative",
    cancelled: "negative",
    refunded: "info",
  };
  return colors[paymentStatus] || "grey";
};

const getPaymentStatusLabel = (paymentStatus) => {
  const labels = {
    paid: "Paid",
    pending: "Pending",
    failed: "Failed",
    cancelled: "Cancelled",
    refunded: "Refunded",
  };
  return labels[paymentStatus] || paymentStatus;
};

// Action methods
const viewDetails = (attendee) => {
  $q.dialog({
    title: "Attendee Details",
    message: `
      <div><strong>Name:</strong> ${attendee.forename} ${attendee.surname}</div>
      <div><strong>Email:</strong> ${attendee.email}</div>
      <div><strong>Status:</strong> ${getStatusLabel(attendee.status)}</div>
      <div><strong>Company:</strong> ${attendee.company || "N/A"}</div>
      <div><strong>Phone:</strong> ${attendee.phone || "N/A"}</div>
    `,
    html: true,
  });
};

const resendInvite = (attendee) => {
  $q.dialog({
    title: "Resend Invitation",
    message: `Resend invitation to ${attendee.forename} ${attendee.surname}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // TODO: Implement resend invite functionality
    $q.notify({
      message: "Invitation resent successfully",
      color: "positive",
      icon: "send",
    });
  });
};

const cancelBooking = (attendee) => {
  $q.dialog({
    title: "Cancel Booking",
    message: `Cancel booking for ${attendee.forename} ${attendee.surname}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // TODO: Implement cancel booking functionality
    $q.notify({
      message: "Booking cancelled successfully",
      color: "positive",
      icon: "cancel",
    });
  });
};

// Lifecycle
onMounted(() => {
  refreshData();
});
</script>
