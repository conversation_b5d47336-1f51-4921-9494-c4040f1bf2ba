<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="text-h6 q-mb-xs">Bookings</div>
    <div class="text-subtitle2 text-grey q-mb-md">
      Manage all bookings for your event
    </div>
    <q-input borderless class="q-mb-lg" />
    <q-markup-table flat bordered> </q-markup-table>
    <TestDataHelper />
  </q-page>
</template>
<script setup>
import { onMounted } from "vue";
import { useDashboardStore } from "@/stores/dashboard";
import TestDataHelper from "./TestDataHelper.vue";

const dashboardStore = useDashboardStore();

//todo: add a generic attendee api call that can be filtered by status
//todo: move to a composable
const getAttendees = async () => {
  const a = await dashboardStore.fetchAttendees();
  const b = await dashboardStore.fetchUnconfirmedAttendees();
  const c = await dashboardStore.fetchCancelledAttendees();
  const d = await dashboardStore.fetchDeclinedAttendees();
  const e = await dashboardStore.fetchFailedPaymentAttendees();
  const f = await dashboardStore.fetchUnpaidBookings();
  console.log("attendees: ", a, b, c, d, e, f);
};

onMounted(() => {
  console.log("AttendeeTable mounted");
  getAttendees();
});
</script>
