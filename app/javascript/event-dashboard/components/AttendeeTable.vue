<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="text-h6 q-mb-xs">Bookings</div>
    <div class="text-subtitle2 text-grey q-mb-md">
      Manage all bookings for your event
    </div>
    <q-input borderless class="q-mb-lg" />
    <q-markup-table flat bordered> </q-markup-table>
  </q-page>
</template>
<script setup>
import { onMounted } from 'vue';

//todo: add a generic attendee api call that can be filtered by status
//todo: move to a composable
const getAttendees = () => {
    
};


onMounted(() => {
  console.log('AttendeeTable mounted');

});
</script>
