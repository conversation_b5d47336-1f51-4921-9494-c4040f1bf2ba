<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="row q-col-gutter-md q-mb-lg items-center justify-between">
      <div class="col-xs-12 col-sm-6">
        <div class="text-h6 q-mb-xs">Attendees</div>
        <div class="text-subtitle2 text-grey q-mb-md">
          Manage all attendees for your event
        </div>
      </div>
      <div class="col-xs-12 col-sm-6">
        <div class="row q-col-gutter-sm items-center justify-end">
          <q-select
            v-model="statusFilter"
            :options="statusOptions"
            label="Filter by Status"
            class="col-6"
            @update:model-value="onFilterChange"
            clearable
            outlined
            dense
          />

          <q-select
            v-model="paymentStatusFilter"
            :options="paymentStatusOptions"
            label="Payment Status"
            class="col-6"
            @update:model-value="onFilterChange"
            clearable
            outlined
            dense
          />
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="row q-col-gutter-md q-mb-lg">
      <q-input
        v-model="searchTerm"
        placeholder="Search by name, email, or company..."
        class="col-12"
        @update:model-value="onSearch"
        debounce="300"
        clearable
        filled
        dense
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>
    </div>

    <!-- Status Summary Cards -->
    <div class="row q-gutter-md q-mb-lg" v-if="statusCounts">
      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-positive">
            {{ statusCounts.confirmed || 0 }}
          </div>
          <div class="text-caption">Confirmed</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-warning">
            {{ statusCounts.unconfirmed || 0 }}
          </div>
          <div class="text-caption">Unconfirmed</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">
            {{ statusCounts.cancelled || 0 }}
          </div>
          <div class="text-caption">Cancelled</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">
            {{ statusCounts.declined || 0 }}
          </div>
          <div class="text-caption">Declined</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-warning">{{ statusCounts.unpaid || 0 }}</div>
          <div class="text-caption">Unpaid</div>
        </q-card-section>
      </q-card>

      <q-card class="col-md-2 col-sm-4 col-xs-6">
        <q-card-section class="text-center">
          <div class="text-h4 text-negative">
            {{ statusCounts.payment_failed || 0 }}
          </div>
          <div class="text-caption">Failed</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Main Table -->
    <q-table
      :rows="attendees"
      :columns="columns"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      @request="onRequest"
      binary-state-sort
      flat
      bordered
      :rows-per-page-options="[25, 50, 100, 200]"
    >
      <!-- Status Column -->
      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-badge
            :color="props.row.status_color"
            :icon="props.row.status_icon"
            :label="getStatusLabel(props.row.status)"
          />
        </q-td>
      </template>

      <!-- Name Column -->
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div class="text-weight-medium">
            {{ props.row.forename }} {{ props.row.surname }}
          </div>
        </q-td>
      </template>

      <!-- Booking Date Column -->
      <template v-slot:body-cell-booking_date="props">
        <q-td :props="props">
          {{ formatDate(props.row.event_booking?.booking_date) }}
        </q-td>
      </template>

      <!-- Payment Status Column -->
      <template v-slot:body-cell-payment_status="props">
        <q-td :props="props">
          <q-chip
            v-if="props.row.event_booking?.payment_status !== undefined"
            :color="
              getPaymentStatusColor(props.row.event_booking.payment_status)
            "
            text-color="white"
            size="sm"
          >
            {{ getPaymentStatusLabel(props.row.event_booking.payment_status) }}
          </q-chip>
          <span v-else class="text-grey">-</span>
        </q-td>
      </template>

      <!-- Booking Count Column -->
      <template v-slot:body-cell-booking_count="props">
        <q-td :props="props">
          <q-badge
            v-if="props.row.event_booking?.booking_count"
            color="primary"
            :label="props.row.event_booking.booking_count"
          />
          <span v-else class="text-grey">0</span>
        </q-td>
      </template>

      <!-- Actions Column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn-dropdown color="primary" icon="more_vert" size="sm" flat dense>
            <q-list>
              <q-item clickable v-close-popup @click="viewDetails(props.row)">
                <q-item-section avatar>
                  <q-icon name="visibility" />
                </q-item-section>
                <q-item-section>View Details</q-item-section>
              </q-item>

              <q-item
                v-if="props.row.status === 'unconfirmed'"
                clickable
                v-close-popup
                @click="resendInvite(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="send" />
                </q-item-section>
                <q-item-section>Resend Invite</q-item-section>
              </q-item>

              <q-item
                v-if="
                  ['confirmed', 'unconfirmed', 'unpaid'].includes(
                    props.row.status
                  )
                "
                clickable
                v-close-popup
                @click="cancelBooking(props.row)"
              >
                <q-item-section avatar>
                  <q-icon name="cancel" />
                </q-item-section>
                <q-item-section>Cancel Booking</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import { format } from "date-fns";
import axios from "axios";

const $q = useQuasar();

// Reactive data
const attendees = ref([]);
const statusCounts = ref(null);
const searchTerm = ref("");
const statusFilter = ref(null);
const paymentStatusFilter = ref(null);
const loading = ref(false);
const eventId = ref(999); // Mock event ID

// Filter options
const statusOptions = [
  { label: "All", value: "all" },
  { label: "Confirmed", value: "confirmed" },
  { label: "Unconfirmed", value: "unconfirmed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Declined", value: "declined" },
  { label: "Payment Failed", value: "payment_failed" },
  { label: "Unpaid", value: "unpaid" },
];

const paymentStatusOptions = [
  { label: "All", value: "all" },
  { label: "Paid", value: "paid" },
  { label: "Unpaid", value: "unpaid" },
  { label: "Part Paid", value: "part_paid" },
  { label: "Free Booking", value: "free_booking" },
  { label: "Payment Failed", value: "payment_failed" },
  { label: "Refunded", value: "refunded" },
];

// Table columns
const columns = [
  {
    name: "status",
    label: "Status",
    field: "status",
    align: "left",
    sortable: true,
  },
  {
    name: "name",
    label: "Name",
    field: (row) => `${row.forename} ${row.surname}`,
    align: "left",
    sortable: true,
    sort: (a, b) => a.localeCompare(b),
  },
  {
    name: "email",
    label: "Email",
    field: "email",
    align: "left",
    sortable: true,
  },
  {
    name: "company",
    label: "Company",
    field: "company",
    align: "left",
    sortable: true,
  },
  {
    name: "booking_count",
    label: "Bookings",
    field: (row) => row.event_booking?.booking_count || 0,
    align: "center",
    sortable: true,
  },
  {
    name: "booking_date",
    label: "Booking Date",
    field: (row) => row.event_booking?.booking_date,
    align: "left",
    sortable: true,
  },
  {
    name: "payment_status",
    label: "Payment",
    field: (row) => row.event_booking?.payment_status,
    align: "left",
    sortable: true,
  },
  {
    name: "actions",
    label: "Actions",
    field: "actions",
    align: "center",
    sortable: false,
  },
];

// Pagination
const pagination = ref({
  sortBy: "name",
  descending: false,
  page: 1,
  rowsPerPage: 25,
  rowsNumber: 0,
});

// Methods
const fetchAttendees = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.value.page,
      per_page: pagination.value.rowsPerPage,
      sortBy: pagination.value.sortBy,
      sortDirection: pagination.value.descending ? "desc" : "asc",
    };

    if (searchTerm.value) params.searchTerm = searchTerm.value;
    if (statusFilter.value && statusFilter.value !== "all")
      params.statusFilter = statusFilter.value;
    if (paymentStatusFilter.value && paymentStatusFilter.value !== "all")
      params.paymentStatusFilter = paymentStatusFilter.value;

    const response = await axios.get(
      `/registered_users/${eventId.value}/show_all.json`,
      { params }
    );

    attendees.value = response.data.users || [];
    statusCounts.value = response.data.status_counts || {};
    pagination.value.rowsNumber = response.data.total_count || 0;
  } catch (error) {
    console.error("Error fetching attendees:", error);
    $q.notify({
      message: "Failed to fetch attendees",
      color: "negative",
      icon: "error",
    });
  } finally {
    loading.value = false;
  }
};

const onRequest = (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  fetchAttendees();
};

const onSearch = () => {
  pagination.value.page = 1;
  fetchAttendees();
};

const onFilterChange = () => {
  pagination.value.page = 1;
  fetchAttendees();
};

const refreshData = () => {
  fetchAttendees();
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return "-";
  try {
    return format(new Date(dateString), "MMM dd, yyyy HH:mm");
  } catch (error) {
    return "-";
  }
};

const getStatusLabel = (status) => {
  const labels = {
    confirmed: "Confirmed",
    unconfirmed: "Unconfirmed",
    cancelled: "Cancelled",
    declined: "Declined",
    payment_failed: "Payment Failed",
    unpaid: "Unpaid",
    no_booking: "No Booking",
  };
  return labels[status] || status;
};

const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    0: "warning", // unpaid
    1: "warning", // part_paid
    2: "positive", // paid
    7: "negative", // payment_failed
  };
  return colors[paymentStatus] || "grey";
};

const getPaymentStatusLabel = (paymentStatus) => {
  const labels = {
    0: "Unpaid",
    1: "Part Paid",
    2: "Paid",
    3: "Not Refunded",
    4: "Refunded",
    5: "Refunded Paid Again",
    6: "Part Refunded",
    7: "Payment Failed",
    8: "Complimentary",
  };
  return labels[paymentStatus] || "Unknown";
};

// Action methods
const viewDetails = (attendee) => {
  $q.dialog({
    title: "Attendee Details",
    message: `
      <div><strong>Name:</strong> ${attendee.forename} ${attendee.surname}</div>
      <div><strong>Email:</strong> ${attendee.email}</div>
      <div><strong>Company:</strong> ${attendee.company || "N/A"}</div>
      <div><strong>Status:</strong> ${getStatusLabel(attendee.status)}</div>
      <div><strong>Booking Count:</strong> ${
        attendee.event_booking?.booking_count || 0
      }</div>
    `,
    html: true,
  });
};

const resendInvite = (attendee) => {
  $q.dialog({
    title: "Resend Invitation",
    message: `Resend invitation to ${attendee.forename} ${attendee.surname}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    $q.notify({
      message: "Invitation resent successfully",
      color: "positive",
      icon: "send",
    });
  });
};

const cancelBooking = (attendee) => {
  $q.dialog({
    title: "Cancel Booking",
    message: `Cancel booking for ${attendee.forename} ${attendee.surname}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    $q.notify({
      message: "Booking cancelled successfully",
      color: "positive",
      icon: "cancel",
    });
  });
};

// Lifecycle
onMounted(() => {
  fetchAttendees();
});
</script>
