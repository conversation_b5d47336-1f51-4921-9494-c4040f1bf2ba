<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div v-if="store.isLoading" class="q-pa-xl text-center">
      <q-spinner color="primary" size="3em" class="q-mb-md" />
      <div class="text-h5">Loading event data...</div>
      <div class="text-body1">
        Please wait while we load the event information
      </div>
    </div>
    <div v-else-if="!store.event">
      <div class="q-pa-xl text-center">
        <div class="text-h5">No event loaded</div>
        <div class="text-body1">Please try again</div>
      </div>
    </div>
    <div v-else class="row justify-between items-center q-gutter-sm">
      <div class="col-12" v-if="store.error">
        <q-banner inline-actions class="bg-negative text-white">
          {{ store.error }}
          <template v-slot:action>
            <q-btn label="Dismiss" flat @click="dismissError" />
          </template>
        </q-banner>
      </div>
      <div class="col-12">
        <div class="text-subtitle1 q-mb-sm">Managing event</div>
        <div class="row justify-between">
          <div
            class="col-10 col-sm-11 text-bold"
            style="font-size: 1.8rem; line-height: 1.2"
          >
            {{ store.event.title }}
          </div>
          <div v-if="store.miniState && enableButtons" class="col-2 col-sm-1">
            <q-btn icon="more_horiz" flat />
          </div>
        </div>
        <div class="row">
          <div v-if="store.event.expired"><q-chip label="Expired" /></div>
          <div>
            <q-chip
              :color="store.event.live ? 'positive' : 'grey'"
              :label="store.event.live ? 'Live' : 'Not Live'"
              text-color="white"
            />
          </div>
          <div>
            <q-chip
              :color="store.event.complete ? 'positive' : 'negative'"
              :label="store.event.complete ? 'Complete' : 'Incomplete'"
              text-color="white"
            />
          </div>
          <div>
            <q-chip
              :color="store.event.is_public ? 'positive' : 'negative'"
              :label="store.event.is_public ? 'Public' : 'Private'"
              text-color="white"
            />
          </div>
        </div>
      </div>

      <div v-if="!store.miniState && enableButtons" class="col-12">
        <div class="text-h6 q-mb-md hg-underline">Event Actions</div>
        <div class="event-actions-grid">
          <cool-button
            icon="edit"
            label="Edit Event"
            description="Modify event details, tickets, and settings"
            variant="primary"
            @click="router.push(`/event/${store.event.id}`)"
          />
          <cool-button
            icon="photo"
            label="Manage Images"
            description="Upload and manage event images"
            variant="secondary"
            @click="router.push(`/event/${store.event.id}/images`)"
          />
          <cool-button
            v-if="!store.event.expired && store.event.complete"
            icon="visibility"
            :label="store.event.live ? 'Make Not Live' : 'Make Live'"
            :description="
              store.event.live
                ? 'Hide event from public view'
                : 'Make event visible to attendees'
            "
            variant="secondary"
            @click="toggleLive"
          />

          <cool-button
            v-if="store.event.live && store.event.complete"
            icon="mail"
            label="Send Invites"
            description="Email invitations to all attendees"
            @click="sendInvites"
          />

          <cool-button
            v-if="
              store.event.live &&
              store.event.complete &&
              paymentStatusData.unpaid !== 0
            "
            icon="hourglass_empty"
            label="Send Reminders"
            description="Remind attendees about pending payments"
            @click="sendPaymentReminders"
          />

          <cool-button
            v-if="store.event.live && store.event.complete"
            icon="public"
            :label="store.event.is_public ? 'Make Private' : 'Make Public'"
            :description="
              store.event.is_public
                ? 'Require invitation to view'
                : 'Allow public registration'
            "
            @click="togglePublic"
          />

          <cool-button
            icon="delete"
            label="Delete Event"
            description="Permanently remove this event"
            variant="danger"
            @click="deleteEvent"
          />
        </div>
      </div>

      <div class="col-12">
        <q-toggle
          label="Receive Booking Summary Email"
          v-model="bookingSummary"
          size="lg"
          toggle-color="primary"
          :options="[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]"
          @update:model-value="toggleSummaryNotificationEmail"
        />
      </div>

      <div v-if="store.event.live && store.event.is_public" class="col-12">
        <q-card flat bordered class="bg-white">
          <q-card-section>
            <div class="text-h6 q-mb-md hg-underline">Public Event URL</div>
            <q-input
              readonly
              v-model="eventUrl"
              label="Public Booking URL"
              stack-label
              class="q-mb-md"
            >
              <template v-slot:append>
                <q-btn
                  flat
                  round
                  icon="content_copy"
                  @click="copyToClipboard(eventUrl)"
                >
                  <q-tooltip>Click to copy URL</q-tooltip>
                </q-btn>
              </template>
            </q-input>
            <div class="text-caption text-grey-7">
              This URL allows anyone to register for your public event. Share
              this link on social media, websites, or in emails.
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <dash-payment-status />
      </div>

      <div class="col-12">
        <q-card flat bordered>
          <q-card-section class="underline">
            <div class="text-h6">Tickets Booked</div>
          </q-card-section>
          <q-card-section>
            <q-markup-table flat bordered>
              <thead>
                <tr>
                  <th></th>
                  <th>Ticket Name</th>
                  <th v-if="hasPaidTickets">
                    Forecast Ticket Revenue (Full Price)
                    <div v-if="store.event.vat_exclusive" class="text-caption">
                      Exc VAT and fees
                    </div>
                  </th>
                  <th v-if="hasPaidTickets">
                    Revenue Received
                    <div v-if="store.event.vat_exclusive" class="text-caption">
                      Exc VAT and fees
                    </div>
                  </th>
                  <th>Ticket Allocation</th>
                  <th>Tickets Booked</th>
                  <th>Tickets Available</th>
                </tr>
              </thead>
              <tbody>
                <template
                  v-for="(ticket, idx) in store.event.tickets"
                  :key="idx"
                >
                  <tr>
                    <td>
                      <!-- Collapse button when expanded -->
                      <q-btn
                        v-if="dropexpanded === idx"
                        flat
                        dense
                        round
                        icon="remove"
                        color="negative"
                        @click="setExpanded(idx, false)"
                      >
                        <q-tooltip>Hide bookings</q-tooltip>
                      </q-btn>

                      <!-- Expand button when collapsed and has bookings -->
                      <q-btn
                        v-else
                        flat
                        dense
                        round
                        icon="add"
                        color="primary"
                        @click="
                          getBookingSummary(ticket.id);
                          setExpanded(idx, true);
                        "
                      >
                        <q-tooltip>Show bookings</q-tooltip>
                      </q-btn>
                    </td>
                    <td>{{ ticket.details }}</td>
                    <td v-if="hasPaidTickets">
                      {{ formatCurrency(ticket.ticket_no * ticket.cost_b) }}
                    </td>
                    <td v-if="hasPaidTickets">
                      {{ formatCurrency(ticket.ticket_revenue) }}
                    </td>
                    <td>{{ ticket.ticket_no }}</td>
                    <td>{{ ticket.ticket_no - ticket.tickets_remaining }}</td>
                    <td>{{ ticket.tickets_remaining }}</td>
                  </tr>

                  <tr v-if="dropexpanded === idx">
                    <td colspan="9">
                      <div class="q-pa-md">
                        <!-- Loading state -->
                        <div v-if="store.isLoading" class="text-center q-py-md">
                          <q-spinner color="primary" size="2em" />
                          <div class="q-mt-sm">Loading bookings...</div>
                        </div>

                        <!-- Bookings table -->
                        <q-markup-table
                          v-else-if="eventBookings.length > 0"
                          flat
                          bordered
                        >
                          <thead>
                            <tr>
                              <th class="text-left">Booker Name</th>
                              <th class="text-right">Ticket Price (each)</th>
                              <th class="text-center">
                                Number of Tickets Booked
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="booking in eventBookings"
                              :key="booking.id"
                            >
                              <td class="text-weight-medium">
                                {{ booking.booker_name }}
                              </td>
                              <td class="text-right">
                                {{ formatCurrency(booking.price_paid) }}
                              </td>
                              <td class="text-center">
                                <q-badge
                                  color="primary"
                                  :label="booking.quantity"
                                />
                              </td>
                            </tr>
                          </tbody>
                        </q-markup-table>

                        <!-- No bookings message -->
                        <div v-else class="text-center q-py-md text-grey-6">
                          <q-icon name="info" size="2em" class="q-mb-sm" />
                          <div>No bookings found for this ticket type</div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>
              </tbody>
            </q-markup-table>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import { useMainStore } from "../../stores/main";
import { useRouter } from "vue-router";
import axios from "axios";

import DashPaymentStatus from "./DashPaymentStatus.vue";
import CoolButton from "@/common/cool-button.vue";

// Setup dependencies
const $q = useQuasar();
const store = useEventStore();
const mainStore = useMainStore();
const router = useRouter();

// Define props to receive event ID from the router
const props = defineProps({
  eventId: {
    type: [String, Number],
    default: null,
  },
});

// Reactive state
const dropexpanded = ref();
const paymentStatusData = ref({
  total: 0,
  paid: 0,
  unpaid: 0,
  partPaid: 0,
  failed: 0,
  pending: 0,
});

// Computed Properties
const bookingSummary = computed(() => {
  return store.event.booking_summary_email || false;
});
const eventBookings = computed(() => store.getEventBookings);
const hasPaidTickets = computed(() => store.getChargeable);

const eventUrl = computed(() => {
  if (!store.event) return "";

  // For public events, use the public booking URL format: /event/:id/:name
  if (store.event.is_public) {
    const sanitizedTitle = store.event.title
      .toLowerCase()
      .replace(/[!?&|$%@':;.<>=+"\\\/\s]/g, "_");

    return `${window.location.origin}/event/${store.event.id}/${sanitizedTitle}`;
  }

  // Fallback for custom URLs or other cases
  return store.event.custom_url ? window.root_url + store.event.custom_url : "";
});
const enableButtons = computed(() => {
  return store.event && !store.event.expired;
});

const dismissError = () => {
  store.error = "";
};

// Format currency for display
const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "GBP",
  }).format(value);
};

// Copy URL to clipboard
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(
    () => {
      $q.notify({
        message: "URL copied to clipboard",
        color: "positive",
        icon: "content_copy",
      });
    },
    (err) => {
      console.error("Could not copy text: ", err);
    }
  );
};

// Methods
const setExpanded = (indx, value) => {
  dropexpanded.value = value ? indx : null;

  // Clear any existing booking data
  store.clearEventBookings();
};

const setPaidCount = (bookingData) => {
  let paidNumber = 0;
  let unPaidNumber = 0;
  let pendingPaidNumber = 0;
  let failedNumber = 0;

  const bLen = bookingData === undefined ? 0 : bookingData.length;
  for (let i = 0; i < bLen; i++) {
    if (bookingData[i].status === "paid" || bookingData[i].free_booking) {
      paidNumber++;
    } else if (bookingData[i].status === "processing") {
      pendingPaidNumber++;
    } else if (bookingData[i].status === "payment_failed") {
      failedNumber++;
    }
  }

  paymentStatusData.value.paid = paidNumber;
  paymentStatusData.value.unpaid = unPaidNumber;
  paymentStatusData.value.failed = failedNumber;
  paymentStatusData.value.pending = pendingPaidNumber;
};

const sendInvites = () => {
  router.push({
    name: "attendees-unconfirmed",
    params: { eventId: store.event.id },
  });
};

const checkStripeAccount = async () => {
  const res = await mainStore.checkStripeAccount();
  return res;
};

const toggleLive = async () => {
  const hasStripeAccount = await checkStripeAccount();
  if (!hasStripeAccount) {
    $q.dialog({
      title: "Stripe details missing",
      message: "You must set stripe account details",
      color: "warning",
      icon: "warning",
      cancel: true,
      persistent: true,
      ok: {
        label: "Go to payment details",
      },
    }).onOk(() => {
      window.location.href = "/dashboard#/payment-admin/fromevent";
    });
    return;
  } else {
    const isLive = store.event.live;
    const action = isLive ? "make not live" : "make live";
    const confirmationMessage = isLive
      ? "This will mean bookings cannot be made, and can only be done if there are no existing bookings"
      : "This will allow bookings to be made for this event";
    const resultMessage = isLive
      ? "Your event is no longer live"
      : "Your event is now live";
    $q.dialog({
      title: "Are you sure?",
      message: confirmationMessage,
      color: "warning",
      icon: "warning",
      cancel: true,
      persistent: true,
    }).onOk(() => {
      store
        .toggleEventLive()
        .then(() => {
          $q.notify({
            message: resultMessage,
            color: "positive",
            icon: "check",
          });
        })
        .catch(() => {
          $q.notify({
            message: "We could not change the live status of this event",
            color: "negative",
            icon: "error",
          });
        });
    });
  }
};

const togglePublic = () => {
  console.log("event: ", store.event);
  const isPublic = store.event.is_public;
  const action = isPublic ? "make private" : "make public";
  const confirmationMessage = isPublic
    ? "This will make your event private and not visible to the public"
    : "This will make your event public and visible to everyone";
  const resultMessage = isPublic
    ? "Your event is now private"
    : "Your event is now public";
  $q.dialog({
    title: "Are you sure?",
    message: confirmationMessage,
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    store
      .toggleEventPublic()
      .then(() => {
        $q.notify({
          message: resultMessage,
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "We could not change the public status of this event",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const getBookingSummary = async (ticket_id) => {
  try {
    await store.loadEventBookings(ticket_id);
  } catch (error) {
    console.error("Error loading booking summary:", error);
    $q.notify({
      message: "Failed to load booking details",
      color: "negative",
      icon: "error",
    });
  }
};

const toggleSummaryNotificationEmail = (toggle) => {
  // Do nothing if no change
  if (
    toggle === store.event.booking_summary_email ||
    (toggle === false && store.event.booking_summary_email === undefined)
  ) {
    return;
  }

  const title = toggle
    ? "Do you wish to receive a booking summary email?"
    : "Do you wish to stop receiving booking summary emails?";

  const message = toggle
    ? "You will receive this daily at 5:00pm"
    : "This will stop your summary emails at 5:00pm each day";

  $q.dialog({
    title: title,
    message: message,
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .put(`/events/${store.event.id}/toggle_booking_notifications.json`, {
        toggle_value: toggle,
      })
      .then(() => {
        $q.notify({
          message: "Successful!",
          color: "positive",
          icon: "check",
        });
        store.event.booking_summary_email = toggle;
      })
      .catch(() => {
        $q.notify({
          message: "Not changed!",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const sendPaymentReminders = () => {
  $q.dialog({
    title: "Are you sure you wish to send reminders?",
    message: "This will email everyone who has not yet fully paid",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .post("/payments/send_reminders", {
        event_id: store.event.id,
      })
      .then(() => {
        $q.notify({
          message: "Emails Sent!",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "Send Failed!",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const deleteEvent = () => {
  $q.dialog({
    title: "Are you sure you wish to delete this event?",
    message: "This cannot be undone",
    color: "negative",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .delete(`/events/${store.event.id}.json`)
      .then(() => {
        $q.notify({
          message: "Event deleted successfully",
          color: "positive",
          icon: "check",
        });
        // Redirect to events list or home page
        window.location.href = "/events/#unified";
      })
      .catch(() => {
        $q.notify({
          message: "Failed to delete event",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const loadEventData = async (eventId) => {
  store.loadEvent(eventId);
};

// Initialize component with single source of truth
onMounted(async () => {
  const targetEventId = props.eventId || store.getEventId;

  if (!targetEventId) {
    store.error = "No event selected";
    $q.notify({
      message: "No event selected. Please select an event from the dropdown.",
      color: "warning",
      position: "top",
      timeout: 3000,
    });
    return;
  }

  await loadEventData(targetEventId);

  // Initialize the expanded array for tickets
  if (store.event?.tickets) {
    dropexpanded.value = new Array(store.event.tickets.length).fill(false);
  }

  //set event bookings
  setPaidCount(store.event.tickets);
});

// Watch for event ID changes with proper dependency tracking
watch(
  () => props.eventId,
  async (newEventId, oldEventId) => {
    if (newEventId && newEventId !== oldEventId) {
      await loadEventData(newEventId);
    }
  },
  { immediate: false }
);

// Watch for store event changes with proper dependency handling
watch(
  () => store.getEvent,
  (newEvent) => {
    if (newEvent && (!store.event || newEvent.id !== store.event.id)) {
      store.event = newEvent;

      // Reinitialize expanded array when event changes
      if (newEvent.tickets) {
        dropexpanded.value = new Array(newEvent.tickets.length).fill(false);
      }
    }
  },
  { immediate: false }
);
</script>

<style scoped>
.viewHeader {
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.hg-underline {
  border-bottom: 2px solid var(--q-primary);
  padding-bottom: 8px;
}

.event-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Small screens and up: 2 columns */
@media (min-width: 600px) {
  .event-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}

/* Medium screens and up: single row with flexbox */
@media (min-width: 900px) {
  .event-actions-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    overflow-x: auto;
  }

  .event-actions-grid > * {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
  }
}
</style>
