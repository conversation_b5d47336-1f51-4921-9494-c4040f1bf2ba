<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <div class="text-h6 q-mb-xs">Event Bookings Integration Test</div>
    <div class="text-subtitle2 text-grey q-mb-md">
      Test the loadEventBookings method with DashMain table integration
    </div>

    <!-- Controls -->
    <div class="row q-gutter-md q-mb-lg">
      <q-btn
        color="primary"
        icon="refresh"
        label="Load All Bookings"
        @click="loadAllBookings"
        :loading="loading"
      />
      
      <q-btn
        color="secondary"
        icon="filter_list"
        label="Load Ticket 1 Bookings"
        @click="loadTicketBookings(1)"
        :loading="loading"
      />
      
      <q-btn
        color="secondary"
        icon="filter_list"
        label="Load Ticket 2 Bookings"
        @click="loadTicketBookings(2)"
        :loading="loading"
      />
      
      <q-btn
        color="warning"
        icon="clear"
        label="Clear Bookings"
        @click="clearBookings"
      />
    </div>

    <!-- Statistics -->
    <div class="row q-gutter-md q-mb-lg" v-if="eventBooking">
      <q-card class="col-md-3 col-sm-6 col-xs-12">
        <q-card-section class="text-center">
          <div class="text-h4 text-primary">{{ eventBooking.bookings_count || 0 }}</div>
          <div class="text-caption">Total Bookings</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-3 col-sm-6 col-xs-12">
        <q-card-section class="text-center">
          <div class="text-h4 text-positive">{{ eventBooking.paid_bookings || 0 }}</div>
          <div class="text-caption">Paid Bookings</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-3 col-sm-6 col-xs-12">
        <q-card-section class="text-center">
          <div class="text-h4 text-green">£{{ eventBooking.total_revenue || '0.00' }}</div>
          <div class="text-caption">Total Revenue</div>
        </q-card-section>
      </q-card>
      
      <q-card class="col-md-3 col-sm-6 col-xs-12">
        <q-card-section class="text-center">
          <div class="text-h4 text-info">{{ eventBooking.total_attendees || 0 }}</div>
          <div class="text-caption">Total Attendees</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- DashMain Style Table (for ticket-specific bookings) -->
    <q-card v-if="eventBookings.length > 0" class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">Ticket-Specific Bookings (DashMain Style)</div>
        <q-markup-table flat bordered>
          <thead>
            <tr>
              <th>Booker Name</th>
              <th>Ticket Price (each)</th>
              <th>Number of Tickets Booked</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="booking in eventBookings" :key="booking.id">
              <td>{{ booking.booker_name }}</td>
              <td>£{{ booking.price_paid }}</td>
              <td>{{ booking.quantity }}</td>
            </tr>
          </tbody>
        </q-markup-table>
      </q-card-section>
    </q-card>

    <!-- Full Bookings Table (for all bookings) -->
    <q-card v-if="eventBooking?.bookings?.length > 0">
      <q-card-section>
        <div class="text-h6 q-mb-md">All Event Bookings</div>
        <q-table
          :rows="eventBooking.bookings.slice(0, 10)"
          :columns="columns"
          row-key="id"
          flat
          bordered
          :pagination="{ rowsPerPage: 10 }"
        >
          <!-- Booker Column -->
          <template v-slot:body-cell-booker="props">
            <q-td :props="props">
              <div>
                <div class="text-weight-medium">
                  {{ props.row.booker.forename }} {{ props.row.booker.surname }}
                </div>
                <div class="text-caption text-grey">{{ props.row.booker.email }}</div>
              </div>
            </q-td>
          </template>

          <!-- Payment Status Column -->
          <template v-slot:body-cell-payment_status="props">
            <q-td :props="props">
              <q-badge
                :color="getPaymentStatusColor(props.row.payment_status)"
                :label="props.row.payment_status"
              />
            </q-td>
          </template>

          <!-- Amount Column -->
          <template v-slot:body-cell-payment_amount="props">
            <q-td :props="props">
              £{{ props.row.payment_amount }}
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- Debug Info -->
    <q-card class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">Debug Information</div>
        <div class="q-mb-sm">
          <strong>Event ID:</strong> {{ eventStore.event?.id || 'Not set' }}
        </div>
        <div class="q-mb-sm">
          <strong>Event Bookings (DashMain):</strong> {{ eventBookings.length }} items
        </div>
        <div class="q-mb-sm">
          <strong>Event Booking (General):</strong> {{ eventBooking ? 'Loaded' : 'Not loaded' }}
        </div>
        <div class="q-mb-sm">
          <strong>Loading State:</strong> {{ loading }}
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";

const $q = useQuasar();
const eventStore = useEventStore();

// Table columns for full bookings
const columns = [
  {
    name: "booking_reference",
    label: "Booking Ref",
    field: "booking_reference",
    align: "left",
  },
  {
    name: "booker",
    label: "Booker",
    field: (row) => `${row.booker.forename} ${row.booker.surname}`,
    align: "left",
  },
  {
    name: "payment_status",
    label: "Payment Status",
    field: "payment_status",
    align: "left",
  },
  {
    name: "payment_amount",
    label: "Amount",
    field: "payment_amount",
    align: "right",
  },
];

// Computed properties
const loading = computed(() => eventStore.isLoading);
const eventBooking = computed(() => eventStore.getEventBooking);
const eventBookings = computed(() => eventStore.getEventBookings);

// Methods
const loadAllBookings = async () => {
  try {
    // Set mock event if needed
    if (!eventStore.event?.id) {
      eventStore.setEvent({
        id: 999,
        title: "Test Event",
        description: "Test event for bookings",
      });
    }

    await eventStore.loadEventBookings();
    $q.notify({
      message: "All bookings loaded successfully",
      color: "positive",
      icon: "check",
    });
  } catch (error) {
    console.error("Error loading all bookings:", error);
    $q.notify({
      message: "Failed to load all bookings",
      color: "negative",
      icon: "error",
    });
  }
};

const loadTicketBookings = async (ticketId) => {
  try {
    // Set mock event if needed
    if (!eventStore.event?.id) {
      eventStore.setEvent({
        id: 999,
        title: "Test Event",
        description: "Test event for bookings",
      });
    }

    await eventStore.loadEventBookings(ticketId);
    $q.notify({
      message: `Ticket ${ticketId} bookings loaded successfully`,
      color: "positive",
      icon: "check",
    });
  } catch (error) {
    console.error(`Error loading ticket ${ticketId} bookings:`, error);
    $q.notify({
      message: `Failed to load ticket ${ticketId} bookings`,
      color: "negative",
      icon: "error",
    });
  }
};

const clearBookings = () => {
  eventStore.clearEventBookings();
  eventStore.setEventBooking(null);
  $q.notify({
    message: "Bookings cleared",
    color: "info",
    icon: "clear",
  });
};

const getPaymentStatusColor = (status) => {
  const colors = {
    paid: "positive",
    pending: "warning",
    failed: "negative",
    cancelled: "negative",
    refunded: "info",
  };
  return colors[status] || "grey";
};

// Initialize
onMounted(() => {
  // Set a mock event for testing
  if (!eventStore.event?.id) {
    eventStore.setEvent({
      id: 999,
      title: "Test Event for Bookings",
      description: "This is a test event for testing booking functionality",
    });
  }
});
</script>
