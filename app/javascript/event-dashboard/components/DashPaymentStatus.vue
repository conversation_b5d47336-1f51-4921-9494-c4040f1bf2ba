<template>
  <q-card flat bordered class="bg-white">
    <q-card-section>
      <div class="text-h6 underline">Payment Status</div>
    </q-card-section>
    <q-card-section>
      <div class="payment-status-grid">
        <div
          class="payment-status-card"
          v-for="(item, index) in dataArray"
          :key="index"
        >
          <q-card
            flat
            bordered
            :id="item.label.replace(/\s+/g, '_').toLowerCase() + '_box'"
          >
            <q-card-section horizontal class="flex items-center">
              <q-card-section>
                <q-avatar :icon="item.icon" :color="item.color" />
              </q-card-section>
              <q-card-section>
                <div class="text-subtitle1">{{ item.label }}</div>
                <div class="text-h6">
                  {{ item.value }} / {{ paymentStatusData.total }}
                </div>
              </q-card-section>
            </q-card-section>
            <q-separator />
            <q-card-actions>
              <q-btn
                v-if="item.route"
                color="primary"
                :to="item.route"
                :label="'View'"
                :disable="!enableButtons"
                class="q-ml-sm"
              />
              <q-btn
                v-else
                color="primary"
                @click="item.onClick && item.onClick()"
                :label="'Send'"
                :disable="
                  !enableButtons ||
                  (item.label === 'Send Reminders' &&
                    paymentStatusData.unpaid === 0)
                "
                class="q-ml-sm"
              />
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, computed } from "vue";
import { useQuasar } from "quasar";
import axios from "axios";
import { useEventStore } from "@/stores/event.js";

const $q = useQuasar();
const store = useEventStore();

const enableButtons = computed(() => {
  return store.event && store.event.live;
});
const paymentStatusData = ref({
  total: 0,
  paid: 0,
  unpaid: 0,
  partPaid: 0,
  failed: 0,
  pending: 0,
});
const dataArray = computed(() => [
  {
    icon: "attach_money",
    label: "Paid",
    value: paymentStatusData.value.paid,
    route: "/payments/paid",
    color: "positive",
  },
  {
    icon: "money",
    label: "Pending Payments",
    value: paymentStatusData.value.pending,
    route: "/payments/processing",
    color: "warning",
  },
  {
    icon: "close",
    label: "Payments Failed",
    value: paymentStatusData.value.failed,
    route: "/attendees-payment-failed",
    color: "negative",
  },
  {
    icon: "hourglass_empty",
    label: "Send Reminders",
    value: paymentStatusData.value.unpaid,
    onClick: sendPaymentReminders,
    color: "grey-7",
  },
]);

const sendPaymentReminders = () => {
  $q.dialog({
    title: "Are you sure you wish to send reminders?",
    message: "This will email everyone who has not yet fully paid",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .post("/payments/send_reminders", {
        event_id: store.event.id,
      })
      .then(() => {
        $q.notify({
          message: "Emails Sent!",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "Send Failed!",
          color: "negative",
          icon: "error",
        });
      });
  });
};
</script>

<style scoped>
.underline {
  border-bottom: 2px solid var(--q-primary);
  padding-bottom: 8px;
}

.payment-status-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

/* Small screens and up: 2 columns */
@media (min-width: 600px) {
  .payment-status-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Large screens and up: 4 columns */
@media (min-width: 1200px) {
  .payment-status-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.payment-status-card {
  width: 100%;
}
</style>
