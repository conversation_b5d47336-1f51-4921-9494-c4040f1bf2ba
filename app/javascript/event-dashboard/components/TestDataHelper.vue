<template>
  <div class="test-data-helper q-pa-md">
    <q-card>
      <q-card-section>
        <div class="text-h6">Test Data Helper</div>
        <div class="text-caption">
          Development tools for testing dashboard components
        </div>
      </q-card-section>

      <q-card-section>
        <div class="q-gutter-md">
          <q-btn
            color="primary"
            @click="enableMockData"
            :disable="mockEnabled"
            label="Enable Mock Data"
            icon="data_usage"
          />

          <q-btn
            color="secondary"
            @click="disableMockData"
            :disable="!mockEnabled"
            label="Disable Mock Data"
            icon="clear"
          />

          <q-btn
            color="positive"
            @click="refreshData"
            label="Refresh Data"
            icon="refresh"
          />
        </div>

        <div class="q-mt-md">
          <q-badge :color="mockEnabled ? 'positive' : 'negative'">
            Mock Data: {{ mockEnabled ? "ENABLED" : "DISABLED" }}
          </q-badge>
        </div>
      </q-card-section>

      <q-card-section v-if="mockEnabled">
        <div class="text-subtitle2 q-mb-md">Mock Data Controls</div>

        <div class="row q-gutter-md">
          <q-btn
            size="sm"
            color="info"
            @click="setMockEvent"
            label="Set Mock Event"
          />

          <q-btn
            size="sm"
            color="info"
            @click="generateAttendees"
            label="Generate Attendees"
          />

          <q-btn
            size="sm"
            color="info"
            @click="generateUnconfirmed"
            label="Generate Unconfirmed"
          />
        </div>

        <div class="q-mt-md">
          <q-input
            v-model.number="attendeeCount"
            type="number"
            label="Number of Attendees"
            style="max-width: 200px"
            min="1"
            max="500"
          />
        </div>
      </q-card-section>

      <q-card-section>
        <div class="text-subtitle2">Current Statistics</div>
        <div class="q-mt-sm">
          <div>Total Attendees: {{ statistics.totalAttendees }}</div>
          <div>Total Confirmed: {{ statistics.totalConfirmed }}</div>
          <div>Total Cancelled: {{ statistics.totalCancelled }}</div>
          <div>Attendees Loaded: {{ attendees.length }}</div>
          <div>Unconfirmed Loaded: {{ unconfirmedAttendees.length }}</div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useDashboardStore } from "@/stores/dashboard";
import { useQuasar } from "quasar";

const $q = useQuasar();
const dashboardStore = useDashboardStore();

// Reactive references
const attendeeCount = ref(50);
const mockEnabled = ref(false);

// Computed properties
const statistics = computed(() => dashboardStore.getStatistics);
const attendees = computed(() => dashboardStore.getAttendees);
const unconfirmedAttendees = computed(
  () => dashboardStore.getUnconfirmedAttendees
);

// Check if mock data is currently enabled
const checkMockStatus = () => {
  mockEnabled.value = window.location.search.includes("mock=true");
};

// Enable mock data by adding query parameter
const enableMockData = () => {
  const url = new URL(window.location);
  url.searchParams.set("mock", "true");
  window.location.href = url.toString();
};

// Disable mock data by removing query parameter
const disableMockData = () => {
  const url = new URL(window.location);
  url.searchParams.delete("mock");
  window.location.href = url.toString();
};

// Set a mock event for testing
const setMockEvent = () => {
  dashboardStore.setEvent({
    id: 999,
    title: "Test Event",
    description: "This is a test event for development",
    datetimefrom: new Date().toISOString(),
    datetimeto: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  });

  $q.notify({
    message: "Mock event set successfully",
    color: "positive",
    icon: "check",
  });
};

// Generate attendees data
const generateAttendees = async () => {
  await dashboardStore.fetchAttendees();
  $q.notify({
    message: `Generated ${attendees.value.length} attendees`,
    color: "positive",
    icon: "people",
  });
};

// Generate unconfirmed attendees data
const generateUnconfirmed = async () => {
  await dashboardStore.fetchUnconfirmedAttendees();
  $q.notify({
    message: `Generated ${unconfirmedAttendees.value.length} unconfirmed attendees`,
    color: "positive",
    icon: "person_add",
  });
};

// Refresh all data
const refreshData = async () => {
  if (!dashboardStore.event.id) {
    setMockEvent();
  }

  await Promise.all([
    dashboardStore.fetchAttendees(),
    dashboardStore.fetchUnconfirmedAttendees(),
    dashboardStore.fetchStatistics(),
  ]);

  $q.notify({
    message: "Data refreshed successfully",
    color: "positive",
    icon: "refresh",
  });
};

onMounted(() => {
  checkMockStatus();
  refreshData();

  // Only show this component in development
  if (process.env.NODE_ENV !== "development") {
    console.warn("TestDataHelper should only be used in development");
  }
});
</script>

<style scoped>
.test-data-helper {
  max-width: 600px;
}
</style>
