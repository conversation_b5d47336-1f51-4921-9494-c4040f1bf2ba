class RegisteredUsersController < ApplicationController
  respond_to :json

  def show
    # For attendees not yet booked, such as those added to email list
    event = Event.find_by_id(params[:id])
    authorize! :manage, event
    # @registered_users_unconfirmed = event.registered_users.includes(:event_booking, :org_user_list).
    #     where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)")

    @registered_users_unconfirmed = event.registered_users.includes(:event_booking, :org_user_list).
    where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL 
            and (declined = false or declined IS NULL) or event_bookings.payment_error IS NOT NULL")

    @unconfirmed_count = @registered_users_unconfirmed.count     

    if params[:attendeeFilter].present?
      @registered_users_unconfirmed = @registered_users_unconfirmed.where("registered_users.forename ILIKE ? or registered_users.surname ILIKE ? or registered_users.email ILIKE ?", '%'+ params[:attendeeFilter] + '%', '%'+ params[:attendeeFilter] + '%', '%'+ params[:attendeeFilter] + '%')
    end

    if params[:tagFilter].present?
      tags = params[:tagFilter].split(',')
      @registered_users_unconfirmed = @registered_users_unconfirmed.by_tags(tags)
    end 

    if params[:optOutFilter].present? && params[:optOutFilter] != "0"
      if params[:optOutFilter] == "1"
        @registered_users_unconfirmed = @registered_users_unconfirmed.opted_out
      else
        @registered_users_unconfirmed = @registered_users_unconfirmed.opted_in
      end
    end

    if params[:inviteFilter].present? && params[:inviteFilter] != "0"
      if params[:inviteFilter] == "1"
        @registered_users_unconfirmed = @registered_users_unconfirmed.invite_sent
      else
        @registered_users_unconfirmed = @registered_users_unconfirmed.invite_unsent
      end
    end

    if params[:eventFilter].present? && params[:eventFilter] != "0"
      @registered_users_unconfirmed = @registered_users_unconfirmed.by_event(params[:eventFilter].to_i)
    end 

    # EventFilter 0 all contacts registered on an event
    if params[:eventStatusFilter].present? && params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1" # attended events
        @registered_users_unconfirmed = @registered_users_unconfirmed.has_attended_events
      elsif params[:eventStatusFilter] == "2" # unattended events
        @registered_users_unconfirmed = @registered_users_unconfirmed.has_unattended_events
      else # cancelled events
        @registered_users_unconfirmed = @registered_users_unconfirmed.has_cancelled_events
      end
    end

    @registered_users_unconfirmed = @registered_users_unconfirmed.has_event_after(params[:dateFromFilter]) if params[:dateFromFilter].present? && params[:dateFromFilter] != "null"
    @registered_users_unconfirmed = @registered_users_unconfirmed.has_event_before(params[:dateToFilter]) if params[:dateFromFilter].present? && params[:dateToFilter] != "null"

    @registered_users_unconfirmed = paginate(@registered_users_unconfirmed)
  end

  def show_all
    event = Event.find_by_id(params[:id])
    authorize! :manage, event

    # Base query with includes for performance
    @registered_users = event.registered_users.includes(:event_booking, :org_user_list, :package_bookings)

    # Search filter - search across name and email
    if params[:searchTerm].present?
      search_term = "%#{params[:searchTerm]}%"
      @registered_users = @registered_users.where(
        "registered_users.forename ILIKE ? OR registered_users.surname ILIKE ? OR registered_users.email ILIKE ? OR registered_users.company ILIKE ?",
        search_term, search_term, search_term, search_term
      )
    end

    # Status filter - filter by booking status
    if params[:statusFilter].present? && params[:statusFilter] != "all"
      case params[:statusFilter]
      when "confirmed"
        # Users with completed bookings (payment_status != 7 is not payment_failed)
        @registered_users = @registered_users.joins(:event_booking)
          .where("event_bookings.booking_count > 0 AND event_bookings.cancelled_at IS NULL")
          .where("event_bookings.payment_status != ? OR event_bookings.free_booking = true", 7)
      when "unconfirmed"
        # Users who haven't completed booking
        @registered_users = @registered_users.joins(:event_booking)
          .where("event_bookings.booking_count = 0 AND event_bookings.cancelled_at IS NULL")
          .where("registered_users.declined = false OR registered_users.declined IS NULL")
      when "cancelled"
        # Users with cancelled bookings
        @registered_users = @registered_users.joins(:event_booking)
          .where("event_bookings.cancelled_at IS NOT NULL")
      when "declined"
        # Users who declined the invitation
        @registered_users = @registered_users.joins(:event_booking)
          .where("registered_users.declined = true")
      when "payment_failed"
        # Users with failed payments
        @registered_users = @registered_users.joins(:event_booking)
          .where("event_bookings.payment_status = ?", 7)
      when "unpaid"
        # Users with pending payments
        @registered_users = @registered_users.joins(:event_booking)
          .where("event_bookings.booking_count > 0 AND event_bookings.cancelled_at IS NULL")
          .where("event_bookings.payment_status IN (?) AND event_bookings.free_booking = false", [0, 1]) # unpaid, part_paid
      end
    end

    # Payment status filter
    if params[:paymentStatusFilter].present? && params[:paymentStatusFilter] != "all"
      @registered_users = @registered_users.joins(:event_booking)
      if params[:paymentStatusFilter] == 'free_booking'
        @registered_users = @registered_users.where("event_bookings.free_booking = true")
      else
        # Convert payment status string to enum integer
        status_mapping = {
          'unpaid' => 0, 'part_paid' => 1, 'paid' => 2, 'not_refunded' => 3,
          'refunded' => 4, 'refunded_paid_again' => 5, 'part_refunded' => 6,
          'payment_failed' => 7, 'complimentary' => 8
        }
        status_int = status_mapping[params[:paymentStatusFilter]]
        @registered_users = @registered_users.where("event_bookings.payment_status = ?", status_int) if status_int
      end
    end

    # Tag filter
    if params[:tagFilter].present?
      tags = params[:tagFilter].split(',')
      @registered_users = @registered_users.by_tags(tags)
    end

    # Opt-out filter
    if params[:optOutFilter].present? && params[:optOutFilter] != "0"
      if params[:optOutFilter] == "1"
        @registered_users = @registered_users.opted_out
      else
        @registered_users = @registered_users.opted_in
      end
    end

    # Sorting
    sort_column = params[:sortBy] || 'id'
    sort_direction = params[:sortDirection] == 'desc' ? 'DESC' : 'ASC'

    case sort_column
    when 'name'
      @registered_users = @registered_users.order("registered_users.forename #{sort_direction}, registered_users.surname #{sort_direction}")
    when 'email'
      @registered_users = @registered_users.order("registered_users.email #{sort_direction}")
    when 'company'
      @registered_users = @registered_users.order("registered_users.company #{sort_direction}")
    when 'booking_date'
      @registered_users = @registered_users.joins(:event_booking).order("event_bookings.created_at #{sort_direction}")
    when 'payment_status'
      @registered_users = @registered_users.joins(:event_booking).order("event_bookings.payment_status #{sort_direction}")
    when 'booking_count'
      @registered_users = @registered_users.joins(:event_booking).order("event_bookings.booking_count #{sort_direction}")
    else
      @registered_users = @registered_users.order("registered_users.id #{sort_direction}")
    end

    # Apply pagination
    @registered_users = paginate_with_sorting(@registered_users)

    unless @registered_users
      render json: {status: 400, error: 'Could Not Retrieve Data'}, status: 400
    end
  end

  def delegate_details
    @user = RegisteredUser.includes(:registered_user_responses).find_by_id(params[:id])
    # authorize! :manage, @user

    if @user
      render :json => @user.to_json(include: :registered_user_responses)
    else
      render json: {error: 'No user available'}, status: 400
    end
  end

# Standalone for attendees
  def show_delegates_attending
    event = Event.find_by_id(params[:id])
    authorize! :manage, event

    @registered_users_confirmed = event.registered_users.joins(:event_booking).where("event_bookings.payment_status != ?", 7).where("(event_bookings.payment_type is not null or event_bookings.free_booking = true) and event_bookings.booking_count > 0 AND event_bookings.cancelled_at IS NULL")
    @unconfirmed_count = event.registered_users.includes(:event_booking, :org_user_list).
      where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").count 

    if params[:attendeeFilter].present?
      @registered_users_confirmed = @registered_users_confirmed.where("forename ILIKE ? or surname ILIKE ? or email ILIKE ?", '%'+ params[:attendeeFilter] + '%', '%'+ params[:attendeeFilter] + '%', '%'+ params[:attendeeFilter] + '%')
    end

    if params[:paymentStatusFilter].present? && params[:paymentStatusFilter] != "null"
      # From the enums in event_bookings
      if params[:paymentStatusFilter] == 'free_booking'
        @registered_users_confirmed = @registered_users_confirmed.where("event_bookings.free_booking = true")
      else
        statusAsInt =  [:unpaid, :part_paid, :paid, :not_refunded, :refunded, :refunded_paid_again, :part_refunded, :payment_failed, :complimentary].index(params[:paymentStatusFilter].to_sym)
        @registered_users_confirmed = @registered_users_confirmed.where("event_bookings.payment_status = ?", statusAsInt)
      end
    end

    if params[:tagFilter].present?
      tags = params[:tagFilter].split(',')
      @registered_users_confirmed = @registered_users_confirmed.by_tags(tags)
    end 

    if params[:optOutFilter] != "0"
      if params[:optOutFilter] == "1"
        @registered_users_confirmed = @registered_users_confirmed.opted_out
      else
        @registered_users_confirmed = @registered_users_confirmed.opted_in
      end
    end

    if params[:eventFilter] != "0"
      @registered_users_confirmed = @registered_users_confirmed.by_event(params[:eventFilter].to_i)
    end 

    # EventFilter 0 all contacts registered on an event
    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1" # attended events
        @registered_users_confirmed = @registered_users_confirmed.has_attended_events
      elsif params[:eventStatusFilter] == "2" # unattended events
        @registered_users_confirmed = @registered_users_confirmed.has_unattended_events
      else # cancelled events
        @registered_users_confirmed = @registered_users_confirmed.has_cancelled_events
      end
    end

    @registered_users_confirmed = @registered_users_confirmed.has_event_after(params[:dateFromFilter]) if params[:dateFromFilter] != "null"
    @registered_users_confirmed = @registered_users_confirmed.has_event_before(params[:dateToFilter]) if params[:dateToFilter] != "null"

    @registered_users_confirmed = paginate(@registered_users_confirmed)

    # goes into show_delegates_attending jbuilder from here
    unless @registered_users_confirmed
      render json: {status: 400, error: 'Could Not Retrieve Data'}, status: 400
    end
  end

  def show_cancelled
    event = Event.find_by_id(params[:id])
    authorize! :manage, event

    @unconfirmed_count = event.registered_users.includes(:event_booking, :org_user_list).
      where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").count

    @registered_users_cancelled = event.registered_users.includes(:event_booking).where("event_bookings.cancelled_at IS NOT NULL").order("event_bookings.cancelled_at")

    @registered_users_cancelled = paginate(@registered_users_cancelled)

    unless @registered_users_cancelled
      render json: {status: 400, error: 'Could Not Retrieve Data'}, status: 400
    end
  end

  def show_declined
    event = Event.find_by_id(params[:id])
    authorize! :manage, event

    @unconfirmed_count = event.registered_users.includes(:event_booking, :org_user_list).
      where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").count

    @registered_users_declined = event.registered_users.includes(:event_booking).where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and registered_users.declined = true")

    @registered_users_declined = paginate(@registered_users_declined)

    unless @registered_users_declined
      render json: {status: 400, error: 'Could Not Retrieve Data'}, status: 400
    end
  end

  def show_payments_failed
    event = Event.find_by_id(params[:id])
    authorize! :manage, event
    @bookings = event.event_bookings.includes('registered_user').where("event_bookings.payment_status = ?", 7)
    @bookings = paginate(@bookings)
  end

  def create
    has_invite = RegisteredUser.where("registered_users.event_id = ? and lower(registered_users.email) = ?", email_params[:event_id], email_params[:email].downcase).joins(:event_booking).last.present?
    if has_invite
      render :json => { :error => "An invitee with this email address already exists for this event" }, :status => 400
      return
    end  
    @reg_user = RegisteredUser.new(email_params)
    unless @reg_user.save
      render json: {error: @reg_user.errors.map { |error| error.message }}, status: 400
    end
  end

  def create_multiple
    reg_users = []

    event = Event.find(params[:registered_users_attributes].first[:event_booking_attributes][:event_id])

    if email_list_params.present?
      email_list_params[:registered_users_attributes].each do |attrs|
        event_booking_attrs = attrs.delete(:event_booking_attributes)
        reg_usr = RegisteredUser.new(attrs)
        reg_usr.build_event_booking(event_id: event_booking_attrs[:event_id], uuid: SecureRandom.uuid)

        # Means there is one already, so just skip
        unless event.event_bookings.joins(:registered_user).where("lower(registered_users.email) = ?", reg_usr.email.downcase).any?
          reg_users << reg_usr
        end
      end
    else
      render json: {status: 400, error: 'Not uploaded'}, status: 400
      return
    end
    if RegisteredUser.import reg_users, recursive: true, validate: false
      reg_users.each do |reg_user|
        reg_user.add_to_org_users_for_external
      end
      render json: {status: 200, users: reg_users}
    else
      render json: {status: 400, error: 'Not uploaded'}, status: 400
    end
  end

  def cancel
    # TODO Add security on this
    registered_user = RegisteredUser.find_by_id(params[:id])

    event_booking = registered_user.event_booking

    if event_booking.cancel_booking(get_cancelled_by(registered_user))
      if Rails.env.development?
        TicketMailer.send_cancellation(event_booking.id).deliver_now
      else
        TicketMailer.send_cancellation(event_booking.id).deliver_later
      end
      render json: {status: 200, cancelled_at: event_booking.cancelled_at, cancelled_by: event_booking.cancelled_by, payment_status: event_booking.payment_status}
    else
      render json: {status: 400, error: 'Event Booking Not Cancelled, has the Event expired?'}
    end
  end

  def update
    registered_user = RegisteredUser.find_by_id(params[:id])
    if registered_user && registered_user.update(reg_update_params)
      # TODO temp removal as did not seem correct emails sent
      # send_reg_update(registered_user.event_booking)
      render json: {success: "Details Updated"}
    else
      render json: {errors: event_booking.errors.values}, status: :unprocessable_entity
    end
  end

  def destroy
    registered_user = RegisteredUser.find_by_id(params[:id])

    unless registered_user
      render json: {errors: ['This user has already been deleted']}, status: 400
      return
    end

    if registered_user.event_booking && registered_user.event_booking.booking_count > 0
      render json: {errors: ['You cannot delete as this user has an active booking']}, status: 400
      return
    end

    if registered_user.destroy
      render json: {status: 200}
    else
      render json: {status: 400, errors: registered_user.errors.full_messages}, status: 400
    end
  end

  def remove_invites
    if current_user.is_an_administrator?
      event = Event.find(params[:event_id])
    else      
      event = current_user.organisation.events.find(params[:event_id])
    end
    job_id = SecureRandom.uuid
    DeleteInvitesJob.set(queue: job_id).perform_later(event.id, job_id)
    render :json => { :queue => job_id }, :status => 200 
  end  

  def poll_remove_invites
    outcome = params[:job_id].present? ? Rails.cache.read(params[:job_id]) : false
    render :json => { :success => outcome}, :status => 200
  end  

  def check_remove_job
    outcome = Rails.cache.read(params[:job_id]).present?
    render :json => { :success => outcome}, :status => 200
  end 

  # Allows users to decline an invitation to an event
  def decline
    registered_user = RegisteredUser.find_by_id(params[:id])
    if registered_user
      if registered_user.update_attribute(:declined, params[:declined])
        if params[:opted_out].present?
          registered_user.org_user_list.update_column(:opted_out, DateTime.now)
        end
        return render json: {status: 200}
      end
    end
    render json: {status: 400}, status: 400
  end

  private

  def send_decline(registered_user)
    if event_booking_id
      if Rails.env.development?
        NotificationMailer.send_decline(registered_user.event_booking_id).deliver_now
      else
        NotificationMailer.send_decline(registered_user.event_booking_id).deliver_later
      end
    end
  end

  def paginate(users)
    @user_count = users.count
    users.page(params[:page]).per(100).order(:id)
  end

  def paginate_with_sorting(users)
    @user_count = users.count
    @total_count = users.count
    per_page = params[:per_page] || 100
    users.page(params[:page]).per(per_page)
  end

  def send_reg_update(event_booking)
    if event_booking
      if Rails.env.development?
        TicketMailer.send_confirmation(event_booking.id, false, true).deliver_now
      else
        TicketMailer.send_confirmation(event_booking.id, false, true).deliver_later
      end
    end
  end

  def notify_waiting_delegates(event_booking)
    waiting_delegate = WaitingList.check_and_return(event_booking)
    if waiting_delegate
      # TODO Quick check another booking hasn't happened
      if RegisteredUser.create(forename: waiting_delegate.forename, surname: waiting_delegate.surname, email: waiting_delegate.email, event_booking_attributes: {event_id: event_booking.event_id, waiting_list_id: waiting_delegate.id})
        # TODO SEND email with link
        if Rails.env.development?
          TicketMailer.send_waiting_list(waiting_delegate).deliver_now
        else
          TicketMailer.send_waiting_list(waiting_delegate).deliver_later
        end
        waiting_delegate.update(expiry_datetime: DateTime.now + 1.days)
        # TODO Create registered_user with event_booking
        # TODO update waiting list that email sent
      end
    end
  end

  # def send_cancellation(event_booking)
  #   package_ids = event_booking.package_bookings.map(&:package_id)
  #   if Rails.env.development?
  #     TicketMailer.send_cancellation(event_booking.id, package_ids).deliver_now
  #   else
  #     TicketMailer.send_cancellation(event_booking.id, package_ids).deliver_later
  #   end
  # end

  def email_params
    params.require(:registered_user).permit(:forename, :surname, :email, :event_id, {event_booking_attributes: [:event_id]})
  end

  def email_list_params
    params.permit({registered_users_attributes: [:forename, :surname, :email, :event_id, {event_booking_attributes: [:event_id]}]})
  end

  def reg_update_params
    params.require(:registered_user).permit(:id, :event_id, :forename, :surname, :company, :title, :phone, :job_description, :email, :address1, :address2, :town, :county, :postcode, :po_number, :user_type,
                                            registered_user_responses_attributes: [:id, :response_type, :text, :selected_option, :registration_field_id])
  end

  def get_cancelled_by(registered_user)
    cancel_user = current_user || registered_user
    cancel_user.name
  end

end
