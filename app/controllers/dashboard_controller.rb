class DashboardController < ApplicationController
  include ActionView::Helpers::NumberHelper

  paginated_action only: [:index]

  respond_to :json

  # before_action :login_required

  before_action :get_event, only: [:show, :confirmed_booking_data]

  layout 'app_new'

  def index
    @kp_per_page = 50
    @kp_page ||= 1

    # @events = if current_user.is_booker?
    #             current_user.events.proper.upcoming.order(:datetimefrom).page(@kp_page).per(@kp_per_page)
    #           else
    #             current_user.organisation.events.proper.upcoming.order(:datetimefrom).page(@kp_page).per(@kp_per_page)
    #           end

    @events = current_user.organisation.events.proper.upcoming.order(:datetimefrom).page(@kp_page).per(@kp_per_page)

    @show_payments_pod = params[:stripe_removed] || params[:stripe_connected] || false
    respond_with @events
  end

  # # Temp to handle vuejs conversion of this page
  # def index_vue
  # end

  def show
    # TODO Do we need all this suggest using jbuilder??!!!! For both requests
    # TODO really just need the event ID

    # Only for preview, a bit heavy!
    @event_json = @event.as_json(include: { legal_term: {}, sponsors: {}, event_type: {}, event_address: {}, payment_option: {},
                                            registration_fields: {}, invite_email_template: {}, confirmation_email_template: {}, file_uploads: {}, discount_codes: {},
                                            packages: { methods: [:tickets_remaining, :ticket_revenue], include: [{ package_options: { include: :package_sub_options } }, :vat_rate] },
                                            tickets: { methods: :tickets_remaining, include: { child_tickets: { methods: :tickets_remaining }, package_options: { include: { package_sub_options: {} } } } },
                                            ticket_groups: { include: { packages: { methods: :tickets_remaining, include: { child_tickets: { methods: :tickets_remaining }, package_options: { include: { package_sub_options: {} } } } } } } },
                                 methods: [:organiser_email, :show_vat, :vat_number]).merge(event_types: EventType.all, no_bookings: @no_bookings).to_json.html_safe

    @hasPaidTickets = @event.chargeable

    # @event_json = @event.as_json(include: {legal_term: {}, event_address: {}, sponsors: {}, registration_fields: {}, packages: {methods: [:tickets_remaining, :ticket_revenue], include: [{package_options: {include: :package_sub_options}}, :vat_rate]}}).to_json.html_safe

    respond_to do |format|
      format.html
      format.json { render :json => @event_json }
    end
  end

  def reports
    org = current_user.organisation
    events = org.events.includes(:booking_payments, :packages, event_bookings: :package_bookings).order('events.title')

    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1"
        events = events.is_live.upcoming
      else
        events = events.expired
      end
    end

    if params[:eventTypeFilter] != "0"
      events = events.filter_by_type(params[:eventTypeFilter]) if params[:eventTypeFilter]
    end

    events = events.filter_event_after(params[:dateFromFilter]) if params[:dateFromFilter].present? && params[:dateFromFilter] != "null"
    events = events.filter_event_before(params[:dateToFilter]) if params[:dateToFilter].present? && params[:dateToFilter] != "null"
    events = events.filter_by_event_name(params[:eventFilter]) if params[:eventFilter].present? && params[:eventFilter] != "null"

    @report = []
    events.each do |e|
      # card payments
      stripe_payments = e.booking_payments.stripe_payments.select { |p| p.refunded.blank? }
      stripe_revenue = stripe_payments.sum { |p| p.amount.to_f }
      stripe_vat = stripe_payments.sum { |p| p.vat_amount.to_f } / 100
      # if e.vatable && stripe_revenue > 0
      #   # assume all packages on event using same vat rate
      #   vat_rate =  20
      #   _vat = (card_revenue / (100 + vat_rate)) * vat_rate
      # end
      stripe_profit = stripe_payments.sum { |p| p.net_amount.to_f } - stripe_vat
      stripe_hg_fees = stripe_payments.sum { |p| p.application_fees.to_f }
      stripe_total_fees = stripe_payments.sum { |p| p.total_fees.to_f }

      # non card payments
      non_stripe_payments = e.booking_payments.non_stripe_payments.select { |p| p.refunded.blank? }
      non_stripe_revenue = non_stripe_payments.sum { |p| p.amount.to_f }
      non_stripe_hg_fees = non_stripe_payments.sum { |p| p.event_booking.app_fees_total.to_f } / 100
      non_stripe_total_fees = non_stripe_hg_fees
      non_stripe_vat = 0
      # if e.vatable && non_stripe_revenue > 0
      #   vat_rate =  20
      #   non_stripe_vat = (non_stripe_revenue / (100 + vat_rate)) * vat_rate
      # end
      non_stripe_profit = non_stripe_revenue - non_stripe_hg_fees - non_stripe_vat

      @report << {
        name: e.title,
        ev_id: e.id,
        vatable: false,
        revenue: number_with_precision(stripe_revenue + non_stripe_revenue, :precision => 2),
        profit: number_with_precision(stripe_profit + non_stripe_profit, :precision => 2),
        vat: number_with_precision(stripe_vat + non_stripe_vat, :precision => 2),
        stripe: number_with_precision(stripe_payments.sum { |p| p.stripe_fees.to_f }, :precision => 2),
        hg_fees: number_with_precision(stripe_hg_fees + non_stripe_hg_fees, :precision => 2),
        total_fees: number_with_precision(stripe_total_fees + non_stripe_total_fees, :precision => 2),
        forecast: number_with_precision(e.packages.sum { |p| p.ticket_no * p.cost_b }, :precision => 2),
        # tickets_sold: e.event_bookings.select { |b| b.cancelled_at.blank? && (b.free_booking == true || (b.payment_status == 'paid' && b.payment_type.present?)) }.map { |eb| eb.package_bookings }.flatten.sum { |p| p.quantity_tickets }
      }
    end

    @total_rows = @report.size

    @grand_totals = {
      revenue: number_with_precision(@report.sum { |r| r[:revenue].to_f }, :precision => 2),
      profit: number_with_precision(@report.sum { |r| r[:profit].to_f }, :precision => 2),
      vat: number_with_precision(@report.sum { |r| r[:vat].to_f }, :precision => 2),
      stripe: number_with_precision(@report.sum { |r| r[:stripe].to_f }, :precision => 2),
      hg_fees: number_with_precision(@report.sum { |r| r[:hg_fees].to_f }, :precision => 2),
      total_fees: number_with_precision(@report.sum { |r| r[:total_fees].to_f }, :precision => 2),
      forecast: number_with_precision(@report.sum { |r| r[:forecast].to_f }, :precision => 2),
      # tickets_sold: @report.sum { |r| r[:tickets_sold] },
    }

    @csv_data = @report

    @report = Kaminari.paginate_array(@report).page(params[:page]).per(100)

    respond_to do |format|
      format.json {}
      format.csv { send_data SummaryReport.export_as_csv(@csv_data, @grand_totals, 'client'), filename: "summary_report.csv" }
      format.html do
        redirect_to root_path
      end
    end
  end

  def version2_reports
    org = current_user.organisation
    events = org.events.includes(:booking_payments, :packages, event_bookings: :package_bookings).order('events.title')

    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1"
        events = events.is_live.upcoming
      else
        events = events.expired
      end
    end

    if params[:eventTypeFilter] != "0"
      events = events.filter_by_type(params[:eventTypeFilter]) if params[:eventTypeFilter]
    end

    events = events.filter_event_after(params[:dateFromFilter]) if params[:dateFromFilter] != "null"
    events = events.filter_event_before(params[:dateToFilter]) if params[:dateToFilter] != "null"
    events = events.filter_by_event_name(params[:eventFilter]) if params[:eventFilter].present? && params[:eventFilter] != "null"

    @report = []
    events.each do |e|
      payments = e.booking_payments.select { |p| p.refunded.blank? }
      revenue = payments.sum { |p| p.amount.to_f }
      vat = payments.sum { |p| p.vat_amount.to_f } / 100
      if e.show_vat? && !e.vat_exclusive? && revenue > 0
        # assume all packages on event using same vat rate
        vat_rate = e&.booking_payments&.first&.event_booking&.packages&.first&.vat_rate&.rate || 20
        vat = (revenue / (100 + vat_rate)) * vat_rate
      end
      @report << {
        name: e.title,
        ev_id: e.id,
        revenue: number_with_precision(revenue, :precision => 2),
        profit: number_with_precision(payments.sum { |p| p.net_amount.to_f } - vat, :precision => 2),
        vat: number_with_precision(vat, :precision => 2),
        stripe: number_with_precision(payments.sum { |p| p.stripe_fees.to_f }, :precision => 2),
        hg_fees: number_with_precision(payments.sum { |p| p.application_fees.to_f }, :precision => 2),
        total_fees: number_with_precision(payments.sum { |p| p.total_fees.to_f }, :precision => 2),
        forecast: number_with_precision(e.packages.sum { |p| p.ticket_no * p.cost_b }, :precision => 2),
        tickets_sold: e.event_bookings.select { |b| b.cancelled_at.blank? && (b.free_booking == true || (b.payment_status == 'paid' && b.payment_type.present?)) }.map { |eb| eb.package_bookings }.flatten.sum { |p| p.quantity_tickets }
      }
    end

    @total_rows = @report.size

    @grand_totals = {
      revenue: number_with_precision(@report.sum { |r| r[:revenue].to_f }, :precision => 2),
      profit: number_with_precision(@report.sum { |r| r[:profit].to_f }, :precision => 2),
      vat: number_with_precision(@report.sum { |r| r[:vat].to_f }, :precision => 2),
      stripe: number_with_precision(@report.sum { |r| r[:stripe].to_f }, :precision => 2),
      hg_fees: number_with_precision(@report.sum { |r| r[:hg_fees].to_f }, :precision => 2),
      total_fees: number_with_precision(@report.sum { |r| r[:total_fees].to_f }, :precision => 2),
      forecast: number_with_precision(@report.sum { |r| r[:forecast].to_f }, :precision => 2),
      tickets_sold: @report.sum { |r| r[:tickets_sold] },
    }

    @csv_data = @report

    @report = Kaminari.paginate_array(@report).page(params[:page]).per(100)

    respond_to do |format|
      format.json {}
      format.csv { send_data SummaryReport.export_as_csv(@csv_data, @grand_totals, 'client'), filename: "summary_report.csv" }
      format.html do
        redirect_to root_path
      end
    end

  end

 def get_event_types
  @event_types = EventType.all.order("name ASC").map do |t|
    { id: t.id, name: t.name || "Unnamed Event Type" }
  end
end

  # def show_vue
  #   # TODO  NO WE DON'T Do we need all this suggest using jbuilder??!!!! For both requests

  #   # Just need event booking??
  #   @event_json = @event.as_json(include: {legal_term: {}, sponsors: {}, packages: {methods: [:tickets_remaining, :ticket_revenue], include: [{package_options: {include: :package_sub_options}}, :vat_rate]}}).to_json.html_safe

  #   respond_to do |format|
  #     format.html
  #     format.json {render :json => @event_json}
  #   end
  # end

  def activate
    @event = Event.find_by_id(params[:id])
    authorize! :manage, @event

    if @event.update_attribute(:approved, true)
      flash[:notice] = 'Event Live'
      format.js
    else
      flash[:error] = @event.errors
      format.js
    end
  end

  def get_event
    @event = Event.includes(packages: [package_options: :package_sub_options]).where(:id => params[:id]).first
    @no_bookings = @event.registered_users.where("user_type = 1").count == 0
    authorize! :manage, @event
  end

  def event_terms
    save_terms
    render json: { status: 200 }
  end

  def payment_info
  end

  def confirmed_booking_data
    @bookings = EventBooking.includes(:booking_payments, package_bookings: :event_booking).where(event_id: params[:id]).
      where("event_bookings.booking_count > 0 AND event_bookings.cancelled_at IS NULL")
      # TODO payment_type doesn't exist until paid but test, now I added bacs or card in from vue-stripe to eventbooking
      #  AND (event_bookings.payment_type is not null or event_bookings.free_booking is true)")
    @bookings_count = @bookings.count

    @bookings_paid_count = @bookings.where(:status => 'paid')
    @unconfirmed_count = event.registered_users.includes(:event_booking, :org_user_list).
      where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").count
  end

  private

  def save_terms
   permitted_params = params.require(:my_terms)
                         .permit(:terms)
                         .merge(
                           amended_by: current_user.email,
                           amended_datetime: DateTime.current
                         )

    organisation = current_user.organisation

    terms_user = organisation.legal_term

    if params[:event_id]
      event = Event.find_by_id(params[:event_id])
      event.update_completion_status("Customise Emails")
      terms_event = event.legal_term
      if terms_event
        terms_event.update(permitted_params)
      else
        event.create_legal_term(permitted_params)
      end
    elsif terms_user
      terms_user.update(permitted_params)
    else
      current_user.organisation.create_legal_term(permitted_params)
    end
  end

end
