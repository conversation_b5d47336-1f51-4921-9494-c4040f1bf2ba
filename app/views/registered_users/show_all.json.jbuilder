json.users @registered_users do |user|
  json.id user.id
  json.email user.email
  json.forename user.forename
  json.surname user.surname
  json.company user.company
  json.phone user.phone
  json.job_title user.job_description
  json.invite_sent user.invite_sent
  json.user_invite_url user.user_invite_url
  json.declined user.declined
  json.created_at user.created_at
  json.updated_at user.updated_at

  # Booking status determination
  if user.event_booking
    booking = user.event_booking
    json.booked (booking.booking_count > 0 && !booking.cancelled_at)

    # Event booking details
    json.event_booking do
      json.id booking.id
      json.booking_count booking.booking_count
      json.booking_date booking.created_at
      json.payment_status booking.payment_status
      json.payment_status_text booking.payment_status_text if booking.respond_to?(:payment_status_text)
      json.payment_type booking.payment_type
      json.free_booking booking.free_booking
      json.cancelled_at booking.cancelled_at
      json.cancelled_by booking.cancelled_by
      json.payment_amount booking.payment_amount if booking.respond_to?(:payment_amount)
      json.uuid booking.uuid
    end

    # Determine overall status
    if booking.cancelled_at.present?
      json.status "cancelled"
      json.status_color "negative"
      json.status_icon "cancel"
    elsif user.declined
      json.status "declined"
      json.status_color "negative"
      json.status_icon "thumb_down"
    elsif booking.payment_status == 7 # payment_failed
      json.status "payment_failed"
      json.status_color "negative"
      json.status_icon "error"
    elsif booking.booking_count > 0
      if booking.free_booking || booking.payment_status == 2 # paid
        json.status "confirmed"
        json.status_color "positive"
        json.status_icon "check_circle"
      else
        json.status "unpaid"
        json.status_color "warning"
        json.status_icon "payment"
      end
    else
      json.status "unconfirmed"
      json.status_color "warning"
      json.status_icon "schedule"
    end
  else
    json.booked false
    json.status "no_booking"
    json.status_color "grey"
    json.status_icon "help"
    json.event_booking nil
  end

  # Opt-out status
  if user.org_user_list
    json.opted_out user.org_user_list.opted_out.present?
    json.opted_out_at user.org_user_list.opted_out
  else
    json.opted_out false
    json.opted_out_at nil
  end
end

# Pagination and metadata
json.total_count @total_count || @user_count
json.user_count @user_count
json.current_page params[:page] || 1
json.per_page params[:per_page] || 100

# Status counts for summary
if @registered_users.respond_to?(:loaded?) && @registered_users.loaded?
  users_array = @registered_users.to_a
else
  users_array = @registered_users
end

json.status_counts do
  json.confirmed users_array.count { |u| u.event_booking&.booking_count&.> 0 && u.event_booking&.cancelled_at.nil? && (u.event_booking&.free_booking || u.event_booking&.payment_status == 2) }
  json.unconfirmed users_array.count { |u| u.event_booking&.booking_count == 0 && u.event_booking&.cancelled_at.nil? && !u.declined }
  json.cancelled users_array.count { |u| u.event_booking&.cancelled_at.present? }
  json.declined users_array.count { |u| u.declined }
  json.payment_failed users_array.count { |u| u.event_booking&.payment_status == 7 }
  json.unpaid users_array.count { |u| u.event_booking&.booking_count&.> 0 && u.event_booking&.cancelled_at.nil? && !u.event_booking&.free_booking && u.event_booking&.payment_status != 2 }
end