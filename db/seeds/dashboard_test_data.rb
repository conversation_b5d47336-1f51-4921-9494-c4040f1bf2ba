# Dashboard Test Data Seeds
# Run with: rails runner db/seeds/dashboard_test_data.rb

puts "Creating test data for dashboard components..."

# Find or create a test organization
org = Organisation.find_or_create_by(name: "Test Organization") do |o|
  o.email = "<EMAIL>"
  o.phone = "+44 1234 567890"
  o.address = "123 Test Street"
  o.city = "Test City"
  o.postcode = "TE5T 1NG"
  o.country = "United Kingdom"
end

# Find or create a test user
user = User.find_or_create_by(email: "<EMAIL>") do |u|
  u.password = "password123"
  u.password_confirmation = "password123"
  u.forename = "Test"
  u.surname = "User"
  u.organisation = org
end

# Create a test event
event = Event.find_or_create_by(title: "Test Dashboard Event") do |e|
  e.organisation = org
  e.user = user
  e.description = "This is a test event for dashboard development"
  e.datetimefrom = 1.month.from_now
  e.datetimeto = 1.month.from_now + 4.hours
  e.live = true
  e.is_public = true
  e.complete = true
  e.max_attendees = 200
end

puts "Created event: #{event.title} (ID: #{event.id})"

# Create test packages/tickets
package = Package.find_or_create_by(event: event, title: "Standard Ticket") do |p|
  p.cost_b = 50.00
  p.ticket_no = 100
  p.description = "Standard event ticket"
end

free_package = Package.find_or_create_by(event: event, title: "Free Ticket") do |p|
  p.cost_b = 0.00
  p.ticket_no = 50
  p.description = "Free event ticket"
end

# Helper method to create registered users with bookings
def create_attendee(event, package, index, status = :confirmed)
  email = "attendee#{index}@example.com"
  
  # Create registered user
  registered_user = RegisteredUser.find_or_create_by(
    event: event,
    email: email
  ) do |ru|
    ru.forename = "John#{index}"
    ru.surname = "Doe#{index}"
    ru.phone = "+44 7700 #{sprintf('%06d', index)}"
    ru.company = "Company #{index}"
    ru.job_title = "Position #{index}"
    ru.user_type = 1 # attendee
  end

  # Create event booking
  event_booking = EventBooking.find_or_create_by(
    event: event,
    registered_user: registered_user
  ) do |eb|
    eb.booking_count = status == :unconfirmed ? 0 : 1
    eb.payment_status = case status
                       when :confirmed then 'paid'
                       when :failed then 7 # payment failed status
                       when :cancelled then 'cancelled'
                       else 'pending'
                       end
    eb.payment_type = package.cost_b > 0 ? 'stripe' : nil
    eb.free_booking = package.cost_b == 0
    eb.cancelled_at = status == :cancelled ? Time.current : nil
  end

  # Create package booking
  unless status == :unconfirmed
    PackageBooking.find_or_create_by(
      event_booking: event_booking,
      package: package,
      registered_user: registered_user
    ) do |pb|
      pb.quantity_tickets = 1
    end
  end

  registered_user
end

# Create confirmed attendees (75)
puts "Creating confirmed attendees..."
75.times do |i|
  pkg = i < 60 ? package : free_package
  create_attendee(event, pkg, i + 1, :confirmed)
end

# Create unconfirmed attendees (30)
puts "Creating unconfirmed attendees..."
30.times do |i|
  create_attendee(event, package, i + 100, :unconfirmed)
end

# Create cancelled attendees (15)
puts "Creating cancelled attendees..."
15.times do |i|
  create_attendee(event, package, i + 200, :cancelled)
end

# Create declined attendees (10)
puts "Creating declined attendees..."
10.times do |i|
  registered_user = create_attendee(event, package, i + 300, :unconfirmed)
  registered_user.update(declined: true)
end

# Create failed payment attendees (8)
puts "Creating failed payment attendees..."
8.times do |i|
  create_attendee(event, package, i + 400, :failed)
end

# Create unpaid bookings (12)
puts "Creating unpaid bookings..."
12.times do |i|
  email = "unpaid#{i}@example.com"
  
  registered_user = RegisteredUser.find_or_create_by(
    event: event,
    email: email
  ) do |ru|
    ru.forename = "Unpaid#{i}"
    ru.surname = "User#{i}"
    ru.phone = "+44 7800 #{sprintf('%06d', i)}"
    ru.user_type = 1
  end

  EventBooking.find_or_create_by(
    event: event,
    registered_user: registered_user
  ) do |eb|
    eb.booking_count = 1
    eb.payment_status = 'pending'
    eb.payment_type = nil
    eb.free_booking = false
  end
end

puts "Test data creation completed!"
puts "Event ID: #{event.id}"
puts "You can now test the dashboard with this event."
puts ""
puts "Summary:"
puts "- Confirmed attendees: 75"
puts "- Unconfirmed attendees: 30" 
puts "- Cancelled attendees: 15"
puts "- Declined attendees: 10"
puts "- Failed payment attendees: 8"
puts "- Unpaid bookings: 12"
